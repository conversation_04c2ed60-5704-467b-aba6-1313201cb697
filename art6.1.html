<!DOCTYPE html>
<html>
<head>
    <title>Audio Reactive Tesseract</title>
    <style>
        body {
            margin: 0;
            overflow: hidden;
            background: black;
        }
        #starfield {
            position: fixed;
            width: 100%;
            height: 100%;
        }
        #audioControls {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            color: #00ff00;
            text-align: center;
        }
        #audioFile {
            display: none;
        }
        .btn {
            background: rgba(0, 255, 0, 0.2);
            border: 1px solid #00ff00;
            color: #00ff00;
            padding: 10px 20px;
            cursor: pointer;
            margin: 5px;
        }
        #visualizerControls {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            color: #00ff00;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border: 1px solid #00ff00;
        }
        .slider-container {
            margin: 10px 0;
        }
        .slider {
            width: 150px;
            margin: 5px 0;
        }
        #stats {
            position: fixed;
            top: 20px;
            left: 20px;
            color: #00ff00;
            font-family: monospace;
            z-index: 1000;
        }
        @keyframes psychedelicWarp {
            0% {
                filter: hue-rotate(0deg) saturate(100%) brightness(100%);
                transform: scale(1) rotate(0deg);
            }
            50% {
                filter: hue-rotate(180deg) saturate(300%) brightness(150%);
                transform: scale(1.1) rotate(5deg);
            }
            100% {
                filter: hue-rotate(360deg) saturate(100%) brightness(100%);
                transform: scale(1) rotate(0deg);
            }
        }

        @keyframes kaleidoscope {
            0% { transform: rotate(0deg) scale(1); }
            25% { transform: rotate(90deg) scale(1.2); }
            50% { transform: rotate(180deg) scale(1); }
            75% { transform: rotate(270deg) scale(1.2); }
            100% { transform: rotate(360deg) scale(1); }
        }

        .trippy-active {
            animation: psychedelicWarp 2s infinite;
        }

        .kaleidoscope-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            background: repeating-conic-gradient(
                from 0deg,
                rgba(255,0,255,0.1) 0deg 10deg,
                rgba(0,255,255,0.1) 10deg 20deg
            );
            mix-blend-mode: screen;
            opacity: 0;
            transition: opacity 0.3s;
        }

        .kaleidoscope-active {
            opacity: 0.5;
            animation: kaleidoscope 4s infinite linear;
        }
    </style>
</head>
<body>
    <canvas id="starfield"></canvas>
    <div id="kaleidoscopeOverlay" class="kaleidoscope-overlay"></div>
    <div id="audioControls">
        <input type="file" id="audioFile" accept="audio/*">
        <button class="btn" onclick="document.getElementById('audioFile').click()">Choose Audio File</button>
        <button class="btn" id="playButton">Play/Pause</button>
        <button class="btn" id="resetButton">Reset View</button>
        <button class="btn" id="recordButton">Start Recording</button>
        <a id="downloadLink" class="btn" style="display: none;">Download Recording</a>
    </div>
    
    <!-- New controls -->
    <div id="visualizerControls">
        <div class="slider-container">
            <label>Rotation Speed</label>
            <input type="range" id="rotationSpeed" class="slider" min="0" max="100" value="50">
        </div>
        <div class="slider-container">
            <label>Size Sensitivity</label>
            <input type="range" id="sizeSensitivity" class="slider" min="0" max="200" value="100">
        </div>
        <div class="slider-container">
            <label>Color Intensity</label>
            <input type="range" id="colorIntensity" class="slider" min="0" max="100" value="80">
        </div>
        <div class="slider-container">
            <label>Glitch Intensity</label>
            <input type="range" id="glitchIntensity" class="slider" min="0" max="100" value="50">
        </div>
        <div class="slider-container">
            <label>Visualizer Size</label>
            <input type="range" id="visualizerSize" class="slider" min="50" max="300" value="150">
        </div>
        <div class="slider-container">
            <label>Visualizer Opacity</label>
            <input type="range" id="visualizerOpacity" class="slider" min="0" max="100" value="70">
        </div>
    </div>

    <div id="stats">
        <div>FPS: <span id="fpsCounter">0</span></div>
        <div>Bass: <span id="bassLevel">0</span></div>
        <div>Mids: <span id="midsLevel">0</span></div>
        <div>Highs: <span id="highsLevel">0</span></div>
    </div>

    <script>
        const canvas = document.getElementById('starfield');
        const ctx = canvas.getContext('2d');
        let audioContext;
        let analyser;
        let audioSource;
        let isPlaying = false;

        // New variables for controls
        let rotationSpeed = 0.5;
        let sizeSensitivity = 1.0;
        let colorIntensity = 0.8;
        let autoRotate = true;
        let angle = 0;
        let glitchIntensity = 0;
        let glitchTimeout = null;
        let isGlitching = false;
        let visualizerSize = 150;
        let visualizerOpacity = 0.7;

        // FPS tracking
        let frameCount = 0;
        let lastTime = performance.now();
        let fps = 0;

        // Add beat detection
        let lastBeatTime = 0;
        const beatThreshold = 150;
        const beatDelay = 200; // Minimum time between beats in ms

        // Add these variables with your other declarations
        let trippyMode = false;
        let lastTrippyTrigger = 0;
        const TRIPPY_COOLDOWN = 1000; // Minimum time between trippy effects

        // Add these variables with your other declarations
        let mediaRecorder;
        let recordedChunks = [];
        let isRecording = false;
        const recordButton = document.getElementById('recordButton');
        const downloadLink = document.getElementById('downloadLink');

        // Initialize recording functionality
        function initRecording() {
            recordButton.addEventListener('click', toggleRecording);
        }

        // Toggle recording state
        function toggleRecording() {
            if (!isRecording) {
                startRecording();
            } else {
                stopRecording();
            }
        }

        // Start screen recording
        function startRecording() {
            recordedChunks = [];
            
            // First ensure audio context is in running state
            if (audioContext && audioContext.state === 'suspended') {
                audioContext.resume();
            }
            
            // Create a new audio destination for recording
            const audioDestination = audioContext.createMediaStreamDestination();
            
            // Create a gain node for the recording audio path
            const recordingGain = audioContext.createGain();
            recordingGain.gain.value = 1.5; // Boost recording volume
            
            // Connect the analyser to the recording path
            // Important: We need to tap into the audio BEFORE the analyser
            if (audioSource) {
                // Connect from the original source if available
                audioSource.connect(recordingGain);
            } else {
                // Otherwise connect from the analyser
                analyser.connect(recordingGain);
            }
            
            recordingGain.connect(audioDestination);
            
            // Get the canvas stream
            const canvasStream = canvas.captureStream(60);
            
            // Get audio tracks from the audio destination
            const audioTracks = audioDestination.stream.getAudioTracks();
            
            // Create a new MediaStream with both video and audio
            const combinedStream = new MediaStream();
            
            // Add all tracks from canvas stream
            canvasStream.getTracks().forEach(track => {
                combinedStream.addTrack(track);
            });
            
            // Add audio tracks
            audioTracks.forEach(track => {
                combinedStream.addTrack(track);
            });
            
            console.log("Audio tracks added:", audioTracks.length);
            console.log("Total tracks in combined stream:", combinedStream.getTracks().length);
            
            // Use WebM with VP9 codec with optimized settings
            mediaRecorder = new MediaRecorder(combinedStream, { 
                mimeType: 'video/webm; codecs=vp9',
                videoBitsPerSecond: 5000000, // 5 Mbps video
                audioBitsPerSecond: 256000   // 256 kbps audio for better quality
            });
            
            mediaRecorder.ondataavailable = (e) => {
                if (e.data.size > 0) {
                    recordedChunks.push(e.data);
                }
            };
            
            mediaRecorder.onstop = () => {
                const blob = new Blob(recordedChunks, { type: 'video/webm' });
                const url = URL.createObjectURL(blob);
                downloadLink.href = url;
                downloadLink.download = 'tesseract-visualization.webm';
                downloadLink.textContent = 'Download WebM';
                downloadLink.style.display = 'inline-block';
                
                // Add conversion options note with AVI included
                const conversionNote = document.createElement('div');
                conversionNote.innerHTML = 'Convert to other formats: ' +
                    '<a href="https://cloudconvert.com/webm-to-avi" target="_blank" style="color:#00ff00;margin:0 5px;">AVI</a> | ' +
                    '<a href="https://cloudconvert.com/webm-to-mov" target="_blank" style="color:#00ff00;margin:0 5px;">MOV</a> | ' +
                    '<a href="https://cloudconvert.com/webm-to-mp4" target="_blank" style="color:#00ff00;margin:0 5px;">MP4</a>';
                conversionNote.style.fontSize = '12px';
                conversionNote.style.marginTop = '5px';
                downloadLink.parentNode.insertBefore(conversionNote, downloadLink.nextSibling);
            };
            
            mediaRecorder.start(1000); // Collect data every second for more reliable recording
            isRecording = true;
            recordButton.textContent = 'Stop Recording';
            recordButton.style.background = 'rgba(255, 0, 0, 0.4)';
        }

        // Stop screen recording
        function stopRecording() {
            mediaRecorder.stop();
            isRecording = false;
            recordButton.textContent = 'Start Recording';
            recordButton.style.background = 'rgba(0, 255, 0, 0.2)';
        }

        function detectBeat(audioData) {
            const now = performance.now();
            if (audioData > beatThreshold && (now - lastBeatTime) > beatDelay) {
                lastBeatTime = now;
                triggerGlitch();
                
                // Add random chance to trigger trippy effect on strong beats
                if (audioData > beatThreshold * 1.5 && Math.random() < 0.3) {
                    triggerTrippyEffect(audioData / 255);
                }
                return true;
            }
            return false;
        }

        // Add pulse effect
        let pulseSize = 1;

        // Add these constants near the top of your script section
        const BEATS_PER_MINUTE = 140;
        const BEATS_PER_SECOND = BEATS_PER_MINUTE / 60;
        const PULSE_AMPLITUDE = 0.3; // How much it grows/shrinks (30% variation)

        function updatePulse(bassLevel) {
            const time = performance.now() / 1000;
            const oscillation = Math.sin(time * BEATS_PER_SECOND * 2 * Math.PI);
            
            // Base pulsing at 140 BPM (reduced impact)
            const basePulseFactor = 1 + (oscillation * PULSE_AMPLITUDE * 0.5);
            
            // Audio-reactive component (affected by sensitivity)
            const audioResponse = (bassLevel / 255) * sizeSensitivity;
            const reactiveScale = 1 + audioResponse;
            
            if (detectBeat(bassLevel)) {
                pulseSize = 1 + (0.5 * sizeSensitivity); // Beat impact scales with sensitivity
            }
            pulseSize = pulseSize * 0.95 + 1 * 0.05;
            
            // Combine base pulse with audio response
            tesseractSize = baseTesseractSize * basePulseFactor * reactiveScale * pulseSize;
        }

        // Audio setup
        function initAudio() {
            audioContext = new (window.AudioContext || window.webkitAudioContext)();
            analyser = audioContext.createAnalyser();
            analyser.fftSize = 256;
        }

        document.getElementById('audioFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            const reader = new FileReader();
            
            reader.onload = function(e) {
                if (audioSource) {
                    audioSource.disconnect();
                }
                
                audioContext.decodeAudioData(e.target.result, function(buffer) {
                    audioSource = audioContext.createBufferSource();
                    audioSource.buffer = buffer;
                    audioSource.connect(analyser);
                    analyser.connect(audioContext.destination);
                    
                    audioSource.start(0);
                    isPlaying = true;
                });
            };
            
            reader.readAsArrayBuffer(file);
        });

        document.getElementById('playButton').addEventListener('click', function() {
            if (audioContext.state === 'suspended') {
                audioContext.resume();
            } else if (audioContext.state === 'running') {
                audioContext.suspend();
            }
        });

        function resizeCanvas() {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        }
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // Grid settings
        const gridSize = 50;
        const gridColor = 'rgba(0, 255, 0, 0.2)';

        // Tesseract settings
        let baseTesseractSize = 100;
        let tesseractSize = baseTesseractSize;
        const tesseractColor = 'rgba(0, 255, 0, 0.8)';

        // Initialize audio
        initAudio();

        // Initialize recording
        initRecording();

        // Vertices of a tesseract in 4D
        const vertices4D = [
            [-1, -1, -1, 1], [1, -1, -1, 1], [1, 1, -1, 1], [-1, 1, -1, 1],
            [-1, -1, 1, 1], [1, -1, 1, 1], [1, 1, 1, 1], [-1, 1, 1, 1],
            [-1, -1, -1, -1], [1, -1, -1, -1], [1, 1, -1, -1], [-1, 1, -1, -1],
            [-1, -1, 1, -1], [1, -1, 1, -1], [1, 1, 1, -1], [-1, 1, 1, -1]
        ];

        // Edges of the tesseract (pairs of vertex indices that should be connected)
        const edges = [
            [0,1], [1,2], [2,3], [3,0], // Front face outer cube
            [4,5], [5,6], [6,7], [7,4], // Back face outer cube
            [0,4], [1,5], [2,6], [3,7], // Connections outer cube
            [8,9], [9,10], [10,11], [11,8], // Front face inner cube
            [12,13], [13,14], [14,15], [15,12], // Back face inner cube
            [8,12], [9,13], [10,14], [11,15], // Connections inner cube
            [0,8], [1,9], [2,10], [3,11], // Connections between cubes front
            [4,12], [5,13], [6,14], [7,15]  // Connections between cubes back
        ];

        // Control event listeners
        document.getElementById('rotationSpeed').addEventListener('input', function(e) {
            rotationSpeed = e.target.value / 50;
        });

        document.getElementById('sizeSensitivity').addEventListener('input', function(e) {
            sizeSensitivity = e.target.value / 100; // Convert to a decimal (0-2.0)
        });

        document.getElementById('colorIntensity').addEventListener('input', function(e) {
            colorIntensity = e.target.value / 100;
        });

        document.getElementById('resetButton').addEventListener('click', function() {
            angle = 0;
            tesseractSize = baseTesseractSize;
        });

        document.getElementById('glitchIntensity').addEventListener('input', function(e) {
            glitchIntensity = e.target.value / 100;
        });

        document.getElementById('visualizerSize').addEventListener('input', function() {
            visualizerSize = parseInt(this.value);
        });

        document.getElementById('visualizerOpacity').addEventListener('input', function() {
            visualizerOpacity = parseInt(this.value) / 100;
        });

        function triggerGlitch() {
            if (!isGlitching) {
                isGlitching = true;
                glitchIntensity = (document.getElementById('glitchIntensity').value / 100) * 2; // Increased intensity
                
                // Create strong visual glitch
                ctx.fillStyle = `rgba(255, 0, 0, 0.5)`;
                ctx.fillRect(0, 0, canvas.width, Math.random() * canvas.height);
                
                // Random screen shake
                canvas.style.transform = `translate(${Math.random() * 20 - 10}px, ${Math.random() * 20 - 10}px)`;
                
                setTimeout(() => {
                    canvas.style.transform = 'none';
                }, 50);

                clearTimeout(glitchTimeout);
                glitchTimeout = setTimeout(() => {
                    isGlitching = false;
                    glitchIntensity = 0;
                }, 150);
            }
        }

        function applyGlitchEffect(points) {
            if (!isGlitching) return points;
            
            // More intense distortion
            const distortedPoints = points.map(point => {
                const glitchAmount = glitchIntensity * 100;
                return {
                    x: point.x + (Math.random() - 0.5) * glitchAmount,
                    y: point.y + (Math.random() - 0.5) * glitchAmount,
                    z: point.z + (Math.random() - 0.5) * glitchAmount
                };
            });

            // Color splitting effect
            ctx.save();
            ctx.globalCompositeOperation = 'screen';
            
            // Draw red offset
            ctx.strokeStyle = 'rgba(255,0,0,0.8)';
            drawTesseract(distortedPoints.map(p => ({...p, x: p.x + 10})));
            
            // Draw blue offset
            ctx.strokeStyle = 'rgba(0,0,255,0.8)';
            drawTesseract(distortedPoints.map(p => ({...p, x: p.x - 10})));
            
            ctx.restore();

            return distortedPoints;
        }

        function updateTesseractSize() {
            if (!analyser) return vertices4D.map(v => v.map(coord => coord * (tesseractSize / 100)));

            const dataArray = new Uint8Array(analyser.frequencyBinCount);
            analyser.getByteFrequencyData(dataArray);

            const bass = dataArray.slice(0, 8).reduce((a, b) => a + b, 0) / 8;
            const mids = dataArray.slice(8, 24).reduce((a, b) => a + b, 0) / 16;
            const highs = dataArray.slice(24, 48).reduce((a, b) => a + b, 0) / 24;

            // Update stats display
            document.getElementById('bassLevel').textContent = Math.round(bass);
            document.getElementById('midsLevel').textContent = Math.round(mids);
            document.getElementById('highsLevel').textContent = Math.round(highs);

            // Enhanced size calculation with sensitivity
            tesseractSize = baseTesseractSize + (bass * 1.5 * sizeSensitivity);
            
            // Auto-rotation plus audio-reactive rotation
            angle += rotationSpeed * 0.02;
            const rotationX = (mids / 255) * Math.PI / 8 + angle;
            const rotationY = (highs / 255) * Math.PI / 8 + angle;

            // Color based on audio levels
            const r = Math.min(255, bass * colorIntensity);
            const g = Math.min(255, mids * colorIntensity);
            const b = Math.min(255, highs * colorIntensity);
            ctx.strokeStyle = `rgba(${r}, ${g}, ${b}, 0.8)`;
            
            // Update FPS counter
            frameCount++;
            const currentTime = performance.now();
            if (currentTime - lastTime >= 1000) {
                fps = frameCount;
                frameCount = 0;
                lastTime = currentTime;
                document.getElementById('fpsCounter').textContent = fps;
            }

            // Return transformed vertices
            return vertices4D.map(vertex => {
                let [x, y, z, w] = vertex;
                
                // Scale based on audio
                x *= tesseractSize / 100;
                y *= tesseractSize / 100;
                z *= tesseractSize / 100;
                w *= tesseractSize / 100;

                // Apply rotations
                [x, y] = [
                    x * Math.cos(rotationX) - y * Math.sin(rotationX),
                    x * Math.sin(rotationX) + y * Math.cos(rotationX)
                ];
                
                [y, z] = [
                    y * Math.cos(rotationY) - z * Math.sin(rotationY),
                    y * Math.sin(rotationY) + z * Math.cos(rotationY)
                ];

                return [x, y, z, w];
            });
        }

        function project4Dto3D(point4D) {
            const w = 2; // Distance from 4D viewpoint
            const distance = 4; // Distance from 3D viewpoint
            
            // Project from 4D to 3D
            let factor = 1 / (w - point4D[3]);
            let x = point4D[0] * factor;
            let y = point4D[1] * factor;
            let z = point4D[2] * factor;
            
            // Project from 3D to 2D
            factor = 1 / (distance - z);
            let x2d = x * factor;
            let y2d = y * factor;
            
            return [x2d * tesseractSize + canvas.width/2, y2d * tesseractSize + canvas.height/2];
        }

        function drawTesseract() {
            const currentVertices4D = updateTesseractSize();
            
            ctx.strokeStyle = tesseractColor;
            ctx.lineWidth = 2;

            const projectedVertices = currentVertices4D.map(vertex => {
                // Project directly without additional rotation
                return project4Dto3D(vertex);
            });

            edges.forEach(edge => {
                const [p1, p2] = edge;
                ctx.beginPath();
                ctx.moveTo(projectedVertices[p1][0], projectedVertices[p1][1]);
                ctx.lineTo(projectedVertices[p2][0], projectedVertices[p2][1]);
                ctx.stroke();
            });
        }

        function drawGrid() {
            ctx.beginPath();
            ctx.strokeStyle = gridColor;
            ctx.lineWidth = 0.5;

            for (let x = 0; x <= canvas.width; x += gridSize) {
                ctx.moveTo(x, 0);
                ctx.lineTo(x, canvas.height);
            }

            for (let y = 0; y <= canvas.height; y += gridSize) {
                ctx.moveTo(0, y);
                ctx.lineTo(canvas.width, y);
            }

            ctx.stroke();
        }

        class Star {
            constructor() {
                this.reset();
            }

            reset() {
                this.x = (Math.random() - 0.5) * canvas.width * 2;
                this.y = (Math.random() - 0.5) * canvas.height * 2;
                this.z = Math.random() * 1500;
                this.baseSize = 0.5 + Math.random() * 0.5;
                this.pulsePhase = Math.random() * Math.PI * 2;
                this.pulseSpeed = 0.03 + Math.random() * 0.02;
                this.greenIntensity = 150 + Math.random() * 105;
                // Pure green colors
                this.coreColor = `rgb(0, 255, 0)`;
                this.glowColor = `rgba(0, 255, 0, 0.3)`;
            }

            update() {
                this.z -= 10;
                if (this.z <= 0) {
                    this.reset();
                    return;
                }
                
                const depth = 1500 - this.z;
                this.size = (depth / 200) * this.baseSize;
                
                this.screenX = (this.x / this.z) * 1500 + canvas.width / 2;
                this.screenY = (this.y / this.z) * 1500 + canvas.height / 2;
                
                if (this.screenX < -50 || this.screenX > canvas.width + 50 ||
                    this.screenY < -50 || this.screenY > canvas.height + 50) {
                    this.reset();
                    return;
                }
                
                this.pulsePhase += this.pulseSpeed;
                this.currentSize = this.size * (1 + 0.15 * Math.sin(this.pulsePhase));
            }

            draw() {
                // Simple two-circle approach for better performance
                // Draw glow
                ctx.beginPath();
                ctx.arc(this.screenX, this.screenY, this.currentSize * 2, 0, Math.PI * 2);
                ctx.fillStyle = this.glowColor;
                ctx.fill();
                
                // Draw core
                ctx.beginPath();
                ctx.arc(this.screenX, this.screenY, this.currentSize, 0, Math.PI * 2);
                ctx.fillStyle = this.coreColor;
                ctx.fill();
            }
        }

        // Reduce number of stars for better performance
        const stars = Array(400).fill().map(() => new Star());

        function drawEqualizer(audioData, centerX, centerY, radius) {
            const bars = 32;
            const barWidth = (Math.PI * 2) / bars;
            
            ctx.beginPath();
            for(let i = 0; i < bars; i++) {
                const amplitude = audioData[i] / 255;
                const barHeight = radius * 0.2 * amplitude;
                const angle = i * barWidth;
                
                const x1 = centerX + Math.cos(angle) * radius;
                const y1 = centerY + Math.sin(angle) * radius;
                const x2 = centerX + Math.cos(angle) * (radius + barHeight);
                const y2 = centerY + Math.sin(angle) * (radius + barHeight);
                
                ctx.moveTo(x1, y1);
                ctx.lineTo(x2, y2);
            }
            ctx.strokeStyle = `rgba(0, 255, 0, ${colorIntensity})`;
            ctx.stroke();
        }

        function updateFPS() {
            frameCount++;
            const currentTime = performance.now();
            const elapsed = currentTime - lastTime;
            
            if (elapsed >= 1000) {
                fps = Math.round((frameCount * 1000) / elapsed);
                document.getElementById('fpsCounter').textContent = fps;
                frameCount = 0;
                lastTime = currentTime;
            }
        }

        function triggerTrippyEffect(intensity) {
            const now = performance.now();
            if (now - lastTrippyTrigger < TRIPPY_COOLDOWN) return;
            
            lastTrippyTrigger = now;
            trippyMode = true;
            
            const canvas = document.getElementById('starfield');
            const overlay = document.getElementById('kaleidoscopeOverlay');
            
            canvas.classList.add('trippy-active');
            overlay.classList.add('kaleidoscope-active');
            
            // Intensify the glitch effect during trippy mode
            glitchIntensity = Math.min(100, glitchIntensity + 30);
            
            // Create ripple effect
            ctx.save();
            ctx.globalCompositeOperation = 'screen';
            const gradient = ctx.createRadialGradient(
                canvas.width/2, canvas.height/2, 0,
                canvas.width/2, canvas.height/2, canvas.width/2
            );
            gradient.addColorStop(0, `hsla(${Math.random() * 360}, 100%, 50%, 0.2)`);
            gradient.addColorStop(1, 'transparent');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            ctx.restore();
            
            setTimeout(() => {
                canvas.classList.remove('trippy-active');
                overlay.classList.remove('kaleidoscope-active');
                trippyMode = false;
            }, 2000);
        }

        function drawCircularVisualizer(audioData) {
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const bars = 64; // Number of bars in the circle
            const barWidth = (Math.PI * 2) / bars;
            
            ctx.save();
            ctx.globalCompositeOperation = 'screen';
            
            // Draw outer circle
            for(let i = 0; i < bars; i++) {
                const amplitude = audioData[i] / 255;
                const barHeight = visualizerSize * 0.5 * amplitude;
                const angle = i * barWidth;
                
                const x1 = centerX + Math.cos(angle) * visualizerSize;
                const y1 = centerY + Math.sin(angle) * visualizerSize;
                const x2 = centerX + Math.cos(angle) * (visualizerSize + barHeight);
                const y2 = centerY + Math.sin(angle) * (visualizerSize + barHeight);
                
                // Create gradient for each bar
                const gradient = ctx.createLinearGradient(x1, y1, x2, y2);
                gradient.addColorStop(0, `rgba(0, 255, 255, ${visualizerOpacity})`);
                gradient.addColorStop(1, `rgba(255, 0, 255, ${visualizerOpacity})`);
                
                ctx.beginPath();
                ctx.lineWidth = 3;
                ctx.strokeStyle = gradient;
                ctx.moveTo(x1, y1);
                ctx.lineTo(x2, y2);
                ctx.stroke();
            }
            
            // Draw inner circle (mirror of outer)
            for(let i = 0; i < bars; i++) {
                const amplitude = audioData[i + bars] / 255;
                const barHeight = visualizerSize * 0.3 * amplitude;
                const angle = i * barWidth;
                
                const x1 = centerX + Math.cos(angle) * (visualizerSize * 0.5);
                const y1 = centerY + Math.sin(angle) * (visualizerSize * 0.5);
                const x2 = centerX + Math.cos(angle) * (visualizerSize * 0.5 - barHeight);
                const y2 = centerY + Math.sin(angle) * (visualizerSize * 0.5 - barHeight);
                
                const gradient = ctx.createLinearGradient(x1, y1, x2, y2);
                gradient.addColorStop(0, `rgba(0, 255, 0, ${visualizerOpacity})`);
                gradient.addColorStop(1, `rgba(255, 255, 0, ${visualizerOpacity})`);
                
                ctx.beginPath();
                ctx.lineWidth = 2;
                ctx.strokeStyle = gradient;
                ctx.moveTo(x1, y1);
                ctx.lineTo(x2, y2);
                ctx.stroke();
            }
            
            ctx.restore();
        }

        function animate() {
            updateFPS();
            ctx.fillStyle = 'rgb(0, 0, 0)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            drawGrid();
            drawTesseract();

            stars.forEach(star => {
                star.update();
                star.draw();
            });

            // Add this before your existing drawing code
            const audioData = new Uint8Array(analyser.frequencyBinCount);
            analyser.getByteFrequencyData(audioData);
            
            if (trippyMode) {
                ctx.save();
                ctx.globalCompositeOperation = 'screen';
                ctx.fillStyle = `hsla(${performance.now() / 20 % 360}, 100%, 50%, 0.1)`;
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                ctx.restore();
                
                // Add wave distortion
                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                const pixels = imageData.data;
                const time = performance.now() / 1000;
                
                for (let y = 0; y < canvas.height; y++) {
                    const wave = Math.sin(y * 0.1 + time) * 10;
                    for (let x = 0; x < canvas.width; x++) {
                        const sourceX = Math.floor(x + wave);
                        if (sourceX >= 0 && sourceX < canvas.width) {
                            const sourceIndex = (y * canvas.width + sourceX) * 4;
                            const targetIndex = (y * canvas.width + x) * 4;
                            pixels[targetIndex] = pixels[sourceIndex];
                            pixels[targetIndex + 1] = pixels[sourceIndex + 1];
                            pixels[targetIndex + 2] = pixels[sourceIndex + 2];
                        }
                    }
                }
                ctx.putImageData(imageData, 0, 0);
            }
            
            if (isPlaying) {
                drawCircularVisualizer(audioData);
            }
            
            requestAnimationFrame(animate);
        }

        animate();
    </script>
</body>
</html>
