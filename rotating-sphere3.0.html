<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rotating 3D Sphere</title>
    <style>
        body {
            margin: 0;
            overflow: hidden;
            background: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }
        canvas {
            display: block;
        }
    </style>
</head>
<body>
    <canvas id="sphereCanvas"></canvas>
    
    <div style="position: fixed; bottom: 20px; left: 50%; transform: translateX(-50%); z-index: 10; text-align: center;">
        <button id="uploadButton" style="background: #333; color: #fff; border: 1px solid #555; padding: 8px 15px; border-radius: 4px; cursor: pointer;">Upload Audio</button>
        <button id="playPauseButton" style="background: #333; color: #fff; border: 1px solid #555; padding: 8px 15px; border-radius: 4px; cursor: pointer; margin-left: 5px;" disabled>Play/Pause</button>
        <button id="recordButton" style="background: #333; color: #fff; border: 1px solid #555; padding: 8px 15px; border-radius: 4px; cursor: pointer; margin-left: 5px;">Record</button>
        <input type="file" id="audioFile" accept="audio/*" style="display: none;">
        <div id="audioStatus" style="margin-top: 8px; font-size: 12px; color: #fff;">No audio loaded</div>
        <a id="downloadLink" style="display: none; background: #333; color: #fff; border: 1px solid #555; padding: 8px 15px; border-radius: 4px; cursor: pointer; margin-top: 5px; text-decoration: none;">Download Recording</a>
    </div>

    <script>
        // Get canvas and context
        const canvas = document.getElementById('sphereCanvas');
        const ctx = canvas.getContext('2d');
        
        // Audio variables
        let audioContext, analyser, audioSource, audioData;
        let isPlaying = false;
        const playPauseButton = document.getElementById('playPauseButton');
        const audioStatus = document.getElementById('audioStatus');
        
        // Add recording variables
        let mediaRecorder;
        let recordedChunks = [];
        let isRecording = false;
        const recordButton = document.getElementById('recordButton');
        const downloadLink = document.getElementById('downloadLink');
        
        // Set canvas size
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
        
        // Handle window resize
        window.addEventListener('resize', () => {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        });
        
        // Configuration
        const config = {
            sphereSize: Math.min(window.innerWidth, window.innerHeight) * 0.3,
            rotationSpeed: 0.01,
            backgroundColor: 'rgba(0, 0, 0, 0.1)',
            pointColor: '#222',
            outlineColor: '#0ff',
            ringCount: 5,  // Number of teleporting rings
            ringSpeed: 0.5,  // Speed of ring movement
            ringAcceleration: 0.2,  // Base acceleration factor for rings
            // Grid system configuration
            grid: {
                layers: 4,  // Number of grid layers
                baseSize: 80,  // Base grid cell size
                maxOpacity: 0.15,  // Maximum grid opacity
                rotationSpeed: 0.005,  // Grid rotation speed
                pulseIntensity: 0.3,  // How much grids pulse with audio
                colors: [
                    'rgba(0, 255, 255, ',  // Cyan
                    'rgba(0, 200, 255, ',  // Light blue
                    'rgba(0, 150, 255, ',  // Blue
                    'rgba(100, 255, 200, ' // Mint
                ]
            }
        };
        
        // Animation variables
        let time = 0;
        let rotation = { x: 0, y: 0, z: 0 };

        // 3D points for sphere shape
        const spherePoints = [];
        // Teleporting rings
        const teleportRings = [];
        // Grid system layers
        const gridLayers = [];
        // Holographic Particle Mesh
        const holographicMesh = {
            particles: [],
            faces: [],
            mirrorFaces: new Set(),
            config: {
                particleCount: 40,
                meshRadius: config.sphereSize * 1.8,
                mirrorProbability: 0.3,
                hologramIntensity: 0.8,
                meshComplexity: 0.7,
                reflectionQuality: 3,
                colorShift: 0,
                energyFlow: 0
            }
        };
        // Quantum Resonance Web
        const quantumWeb = {
            nodes: [],
            connections: [],
            resonanceField: [],
            config: {
                nodeCount: 25,
                maxConnections: 4,
                connectionDistance: 150,
                resonanceRadius: 80,
                nodeLifespan: 300, // frames
                energyDecay: 0.95,
                quantumFluctuation: 0.02,
                harmonicLayers: 3
            }
        };
        
        // Initialize audio context
        function initAudio() {
            audioContext = new (window.AudioContext || window.webkitAudioContext)();
            analyser = audioContext.createAnalyser();
            // Smaller FFT size for more responsive analysis
            analyser.fftSize = 256; // Reduced from default
            audioData = new Uint8Array(analyser.frequencyBinCount);
        }
        
        // Handle audio file upload
        function handleAudioFile(e) {
            const file = e.target.files[0];
            if (!file) return;
            
            const reader = new FileReader();
            audioStatus.textContent = "Loading audio...";
            audioStatus.style.color = "#ffcc00";
            
            reader.onload = function(e) {
                if (!audioContext) initAudio();
                
                if (audioSource) {
                    audioSource.disconnect();
                }
                
                audioContext.decodeAudioData(e.target.result, function(buffer) {
                    audioSource = audioContext.createBufferSource();
                    audioSource.buffer = buffer;
                    audioSource.connect(analyser);
                    analyser.connect(audioContext.destination);
                    
                    audioSource.loop = false; // Change from true to false
                    
                    // Add ended event listener
                    audioSource.onended = function() {
                        isPlaying = false;
                        playPauseButton.textContent = "Play";
                        playPauseButton.disabled = false;
                        audioStatus.textContent = "Playback ended";
                    };
                    
                    audioSource.start(0);
                    isPlaying = true;
                    
                    // Enable and update play/pause button
                    playPauseButton.disabled = false;
                    playPauseButton.textContent = "Pause";
                    
                    audioStatus.textContent = "Playing: " + file.name;
                    audioStatus.style.color = "#00ff66";
                });
            };
            
            reader.readAsArrayBuffer(file);
        }
        
        // Toggle play/pause function
        function togglePlayPause() {
            if (!audioContext || !audioSource) return;
            
            if (isPlaying) {
                // Pause audio
                audioContext.suspend();
                isPlaying = false;
                playPauseButton.textContent = "Play";
            } else {
                // Resume audio
                audioContext.resume();
                isPlaying = true;
                playPauseButton.textContent = "Pause";
            }
        }
        
        // Analyze audio and get frequency bands
        function analyzeAudio() {
            if (!analyser || !isPlaying) {
                return [0, 0, 0, 0, 0]; // Default values when no audio
            }
            
            analyser.getByteFrequencyData(audioData);
            
            // Split frequency data into 5 bands
            const bandSize = Math.floor(audioData.length / 5);
            const bands = [];
            
            for (let i = 0; i < 5; i++) {
                const start = i * bandSize;
                const end = start + bandSize;
                let sum = 0;
                
                for (let j = start; j < end; j++) {
                    sum += audioData[j];
                }
                
                // Normalize to 0-1 and apply faster response
                const value = sum / bandSize / 255;
                bands.push(value); 
            }
            
            return bands;
        }
        
        // Generate sphere points
        function generateSpherePoints() {
            // Clear existing points
            spherePoints.length = 0;
            
            // Generate sphere using latitude and longitude lines
            const latitudeLines = 30;
            const longitudeLines = 30;
            
            // Create latitude lines (horizontal circles)
            for (let lat = 0; lat <= latitudeLines; lat++) {
                const phi = Math.PI * lat / latitudeLines;
                const y = Math.cos(phi);
                const radius = Math.sin(phi);
                
                for (let lon = 0; lon < longitudeLines; lon++) {
                    const theta = 2 * Math.PI * lon / longitudeLines;
                    const x = Math.cos(theta) * radius;
                    const z = Math.sin(theta) * radius;
                    
                    // Scale to size
                    spherePoints.push({ 
                        x: x * config.sphereSize,
                        y: y * config.sphereSize,
                        z: z * config.sphereSize,
                        baseX: x * config.sphereSize,
                        baseY: y * config.sphereSize,
                        baseZ: z * config.sphereSize,
                        freqBand: Math.floor(lat / latitudeLines * 5), // Assign frequency band
                        isLatitude: true
                    });
                }
            }
            
            // Create longitude lines (vertical half-circles)
            for (let lon = 0; lon < longitudeLines; lon++) {
                const theta = 2 * Math.PI * lon / longitudeLines;
                
                for (let lat = 0; lat <= latitudeLines; lat++) {
                    const phi = Math.PI * lat / latitudeLines;
                    const y = Math.cos(phi);
                    const radius = Math.sin(phi);
                    
                    const x = Math.cos(theta) * radius;
                    const z = Math.sin(theta) * radius;
                    
                    // Scale to size
                    spherePoints.push({ 
                        x: x * config.sphereSize,
                        y: y * config.sphereSize,
                        z: z * config.sphereSize,
                        baseX: x * config.sphereSize,
                        baseY: y * config.sphereSize,
                        baseZ: z * config.sphereSize,
                        freqBand: Math.floor(lon / longitudeLines * 5), // Assign frequency band
                        isLongitude: true
                    });
                }
            }
        }
        
        // Generate teleporting rings
        function generateTeleportRings() {
            teleportRings.length = 0;

            for (let i = 0; i < config.ringCount; i++) {
                // Assign a frequency band to each ring (0-4)
                const freqBand = Math.floor(Math.random() * 5);

                teleportRings.push({
                    radius: config.sphereSize * (1.2 + Math.random() * 0.5),
                    position: Math.random() * Math.PI * 2, // Random position around the sphere
                    speed: (0.5 + Math.random() * 0.5) * config.ringSpeed * (Math.random() > 0.5 ? 1 : -1),
                    baseSpeed: (0.5 + Math.random() * 0.5) * config.ringSpeed * (Math.random() > 0.5 ? 1 : -1),
                    thickness: 1 + Math.random() * 2,
                    freqBand: freqBand, // Store the frequency band for this ring
                    opacity: 0.2 + Math.random() * 0.5,
                    lastBassLevel: 0
                });
            }
        }

        // Generate multi-layered grid system
        function generateGridLayers() {
            gridLayers.length = 0;

            for (let layer = 0; layer < config.grid.layers; layer++) {
                const gridLayer = {
                    size: config.grid.baseSize * (1 + layer * 0.5), // Increasing size per layer
                    rotation: Math.random() * Math.PI * 2, // Random initial rotation
                    rotationSpeed: config.grid.rotationSpeed * (1 + layer * 0.3) * (Math.random() > 0.5 ? 1 : -1),
                    opacity: config.grid.maxOpacity * (1 - layer * 0.2), // Decreasing opacity per layer
                    color: config.grid.colors[layer % config.grid.colors.length],
                    freqBand: layer % 5, // Assign frequency band
                    angle: (Math.PI / 6) * layer, // Different angle for each layer
                    depth: layer * 100 - 200, // Z-depth for layering
                    lineWidth: 1 + layer * 0.3
                };
                gridLayers.push(gridLayer);
            }
        }

        // Generate holographic particle mesh
        function generateHolographicMesh() {
            holographicMesh.particles = [];
            holographicMesh.faces = [];
            holographicMesh.mirrorFaces.clear();

            // Create particles in a complex 3D formation
            for (let i = 0; i < holographicMesh.config.particleCount; i++) {
                // Use multiple geometric patterns for interesting distribution
                const phi = Math.acos(1 - 2 * Math.random()); // Uniform sphere distribution
                const theta = Math.random() * Math.PI * 2;
                const r = holographicMesh.config.meshRadius * (0.6 + Math.random() * 0.4);

                // Add some geometric structure
                const layer = Math.floor(i / 8);
                const layerAngle = (i % 8) * Math.PI / 4;
                const layerRadius = r * (0.8 + layer * 0.1);

                const particle = {
                    id: i,
                    x: Math.sin(phi) * Math.cos(theta) * layerRadius,
                    y: Math.cos(phi) * layerRadius,
                    z: Math.sin(phi) * Math.sin(theta) * layerRadius,
                    baseX: Math.sin(phi) * Math.cos(theta) * layerRadius,
                    baseY: Math.cos(phi) * layerRadius,
                    baseZ: Math.sin(phi) * Math.sin(theta) * layerRadius,
                    energy: Math.random(),
                    frequency: Math.floor(Math.random() * 5),
                    phase: Math.random() * Math.PI * 2,
                    connections: []
                };

                holographicMesh.particles.push(particle);
            }

            // Create mesh faces using Delaunay-inspired triangulation
            createMeshFaces();

            // Randomly select faces to be mirrors
            selectMirrorFaces();
        }
        
        // Create mesh faces for holographic effect
        function createMeshFaces() {
            holographicMesh.faces = [];

            // Create faces by connecting nearby particles
            for (let i = 0; i < holographicMesh.particles.length; i++) {
                const particle = holographicMesh.particles[i];
                const nearbyParticles = [];

                // Find nearby particles
                for (let j = 0; j < holographicMesh.particles.length; j++) {
                    if (i === j) continue;

                    const other = holographicMesh.particles[j];
                    const distance = Math.sqrt(
                        Math.pow(particle.x - other.x, 2) +
                        Math.pow(particle.y - other.y, 2) +
                        Math.pow(particle.z - other.z, 2)
                    );

                    if (distance < holographicMesh.config.meshRadius * 0.8) {
                        nearbyParticles.push({ particle: other, distance, index: j });
                    }
                }

                // Sort by distance and create triangular faces
                nearbyParticles.sort((a, b) => a.distance - b.distance);

                // Create faces with the closest particles
                for (let k = 0; k < Math.min(nearbyParticles.length - 1, 3); k++) {
                    for (let l = k + 1; l < Math.min(nearbyParticles.length, 4); l++) {
                        const face = {
                            particles: [i, nearbyParticles[k].index, nearbyParticles[l].index],
                            center: {
                                x: (particle.x + nearbyParticles[k].particle.x + nearbyParticles[l].particle.x) / 3,
                                y: (particle.y + nearbyParticles[k].particle.y + nearbyParticles[l].particle.y) / 3,
                                z: (particle.z + nearbyParticles[k].particle.z + nearbyParticles[l].particle.z) / 3
                            },
                            normal: calculateFaceNormal(particle, nearbyParticles[k].particle, nearbyParticles[l].particle),
                            energy: (particle.energy + nearbyParticles[k].particle.energy + nearbyParticles[l].particle.energy) / 3
                        };

                        holographicMesh.faces.push(face);
                    }
                }
            }
        }

        // Calculate face normal for lighting
        function calculateFaceNormal(p1, p2, p3) {
            const v1 = { x: p2.x - p1.x, y: p2.y - p1.y, z: p2.z - p1.z };
            const v2 = { x: p3.x - p1.x, y: p3.y - p1.y, z: p3.z - p1.z };

            return {
                x: v1.y * v2.z - v1.z * v2.y,
                y: v1.z * v2.x - v1.x * v2.z,
                z: v1.x * v2.y - v1.y * v2.x
            };
        }

        // Select random faces to be mirrors
        function selectMirrorFaces() {
            holographicMesh.mirrorFaces.clear();

            for (let i = 0; i < holographicMesh.faces.length; i++) {
                if (Math.random() < holographicMesh.config.mirrorProbability) {
                    holographicMesh.mirrorFaces.add(i);
                }
            }
        }

        // Get holographic color based on angle and audio
        function getHolographicColor(angle, audioIntensity, isMirror = false) {
            const time = Date.now() * 0.001;
            const baseHue = (angle * 180 / Math.PI + time * 30 + holographicMesh.config.colorShift) % 360;

            // Holographic color palette: cyan, magenta, yellow, with rainbow shifts
            const hue1 = (baseHue + audioIntensity * 60) % 360;
            const hue2 = (baseHue + 120 + audioIntensity * 40) % 360;
            const hue3 = (baseHue + 240 + audioIntensity * 80) % 360;

            const saturation = 80 + audioIntensity * 20;
            const lightness = isMirror ? 70 + audioIntensity * 30 : 50 + audioIntensity * 25;
            const alpha = isMirror ? 0.9 : 0.6 + audioIntensity * 0.3;

            // Create gradient-like effect by mixing colors
            const mixRatio = Math.sin(time * 2 + angle) * 0.5 + 0.5;
            const finalHue = hue1 * mixRatio + hue2 * (1 - mixRatio);

            return `hsla(${finalHue}, ${saturation}%, ${lightness}%, ${alpha})`;
        }

        // Rotate point around all axes
        function rotatePoint(point) {
            // X-axis rotation
            let y = point.y * Math.cos(rotation.x) - point.z * Math.sin(rotation.x);
            let z = point.y * Math.sin(rotation.x) + point.z * Math.cos(rotation.x);
            
            // Y-axis rotation
            let x = point.x * Math.cos(rotation.y) + z * Math.sin(rotation.y);
            z = -point.x * Math.sin(rotation.y) + z * Math.cos(rotation.y);
            
            // Z-axis rotation
            const tempX = x;
            x = x * Math.cos(rotation.z) - y * Math.sin(rotation.z);
            y = tempX * Math.sin(rotation.z) + y * Math.cos(rotation.z);
            
            return { x, y, z };
        }
        
        // Project 3D point to 2D
        function projectPoint(point) {
            const fov = 300;
            const viewZ = 1.5 * config.sphereSize;
            const scale = fov / (fov + point.z + viewZ);
            
            return {
                x: canvas.width / 2 + point.x * scale,
                y: canvas.height / 2 + point.y * scale,
                scale: scale
            };
        }
        
        // Animation loop
        function animate() {
            // Clear canvas with fade effect
            ctx.fillStyle = config.backgroundColor;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Update time
            time += 0.01;
            
            // Update rotation
            rotation.y += config.rotationSpeed;
            rotation.x = Math.sin(time * 0.3) * 0.2;
            
            // Analyze audio
            const frequencyBands = analyzeAudio();

            // Draw background grid layers first
            drawGridLayers(frequencyBands);

            // Update and draw holographic mesh
            updateHolographicMesh(frequencyBands);
            drawHolographicMesh(frequencyBands);

            // Update points based on audio
            for (const point of spherePoints) {
                const band = frequencyBands[point.freqBand];
                const waveIntensity = band * 0.5; // Scale factor for wave effect
                
                // Calculate displacement direction (normal to sphere surface)
                const normalX = point.baseX / config.sphereSize;
                const normalY = point.baseY / config.sphereSize;
                const normalZ = point.baseZ / config.sphereSize;
                
                // Apply wave effect based on audio frequency
                // Move points along their normal vector instead of scaling
                const displacement = waveIntensity * 20; // Max displacement in pixels
                
                point.x = point.baseX + normalX * displacement;
                point.y = point.baseY + normalY * displacement;
                point.z = point.baseZ + normalZ * displacement;
            }
            
            // Project points to 2D
            const projectedPoints = spherePoints.map(point => {
                const rotated = rotatePoint(point);
                return {
                    original: point,
                    rotated: rotated,
                    projected: projectPoint(rotated)
                };
            });
            
            // Sort points by z-depth
            projectedPoints.sort((a, b) => b.rotated.z - a.rotated.z);
            
            // Draw circular visualizer
            drawCircularVisualizer(frequencyBands);
            
            // Draw teleporting rings
            drawTeleportRings(frequencyBands);
            
            // Draw points
            for (const point of projectedPoints) {
                // Get frequency band for this point
                const band = frequencyBands[point.original.freqBand];
                
                // Adjust size based on audio
                const size = (2 + band * 5) * point.projected.scale;
                
                // Adjust color based on audio intensity
                const hue = 180 + band * 180; // Cyan to pink
                const brightness = 50 + band * 50; // Brighter with higher intensity
                
                // Draw point with glow
                ctx.shadowBlur = 5 + band * 10;
                ctx.shadowColor = `hsl(180, 100%, 50%)`;
                
                ctx.fillStyle = `hsl(180, 80%, 25%)`;
                ctx.beginPath();
                ctx.arc(point.projected.x, point.projected.y, size, 0, Math.PI * 2);
                ctx.fill();
                
                // Reset shadow for performance
                ctx.shadowBlur = 0;
            }
            
            // Add occasional glitch effect
            if (Math.random() > 0.98) {
                addGlitchEffect();
            }
            
            requestAnimationFrame(animate);
        }
        
        // Draw teleporting rings
        function drawTeleportRings(frequencyBands) {
            for (const ring of teleportRings) {
                // Get bass level (using the first frequency band)
                const bassLevel = frequencyBands[0];
                
                // Calculate acceleration based on bass intensity and changes
                const bassDelta = Math.max(0, bassLevel - ring.lastBassLevel);
                const acceleration = bassDelta * config.ringAcceleration;
                
                // Apply acceleration to ring speed
                if (bassLevel > 0.2) {
                    // Accelerate in current direction
                    ring.speed += (ring.speed > 0 ? 1 : -1) * acceleration;
                } else {
                    // Gradually return to base speed
                    ring.speed = ring.speed * 0.95 + ring.baseSpeed * 0.05;
                }
                
                // Store current bass level for next frame
                ring.lastBassLevel = bassLevel;
                
                // Update ring position with current speed
                ring.position += ring.speed * 0.02;
                
                // Reset position if it goes out of bounds
                if (ring.position > Math.PI * 2) ring.position = 0;
                if (ring.position < 0) ring.position = Math.PI * 2;
                
                // Calculate ring orientation based on position
                const phi = ring.position;
                
                // Get frequency band for this ring
                const band = frequencyBands[ring.freqBand];
                const audioIntensity = band;
                const pulseSize = 1 + audioIntensity * 0.3;
                const currentRadius = ring.radius * pulseSize;
                
                // Use fixed color for rings
                const ringColor = `hsl(180, 100%, 70%)`;
                
                // Draw ring
                ctx.beginPath();
                
                for (let theta = 0; theta < Math.PI * 2; theta += 0.05) {
                    const x = Math.sin(phi) * Math.sin(theta) * currentRadius;
                    const y = Math.cos(phi) * currentRadius;
                    const z = Math.sin(phi) * Math.cos(theta) * currentRadius;
                    
                    // Rotate point
                    const rotated = rotatePoint({x, y, z});
                    const projected = projectPoint(rotated);
                    
                    if (theta === 0) {
                        ctx.moveTo(projected.x, projected.y);
                    } else {
                        ctx.lineTo(projected.x, projected.y);
                    }
                }
                
                ctx.closePath();
                
                // Set ring style with glow effect
                ctx.strokeStyle = ringColor.replace(')', `, ${ring.opacity + audioIntensity * 0.3})`).replace('hsl', 'hsla');
                ctx.lineWidth = ring.thickness * (1 + audioIntensity * 0.3);
                
                // Add glow effect
                ctx.shadowBlur = 15 * (1 + audioIntensity);
                ctx.shadowColor = ringColor;
                
                ctx.stroke();
                
                // Reset shadow for performance
                ctx.shadowBlur = 0;
            }
        }
        
        // Draw multi-layered grid system
        function drawGridLayers(frequencyBands) {
            ctx.save();
            ctx.globalCompositeOperation = 'screen';

            // Sort layers by depth (back to front)
            const sortedLayers = [...gridLayers].sort((a, b) => a.depth - b.depth);

            for (const layer of sortedLayers) {
                // Get frequency band for this layer
                const band = frequencyBands[layer.freqBand];
                const audioIntensity = band * config.grid.pulseIntensity;

                // Update layer rotation
                layer.rotation += layer.rotationSpeed;

                // Calculate dynamic opacity based on audio
                const dynamicOpacity = layer.opacity + audioIntensity * 0.1;
                const currentSize = layer.size * (1 + audioIntensity * 0.2);

                // Set up drawing context
                ctx.strokeStyle = layer.color + dynamicOpacity + ')';
                ctx.lineWidth = layer.lineWidth * (1 + audioIntensity * 0.5);
                ctx.shadowBlur = 3 + audioIntensity * 8;
                ctx.shadowColor = layer.color + '0.3)';

                // Draw grid at different angles and positions
                drawGrid(currentSize, layer.rotation, layer.angle, layer.depth);

                // Reset shadow
                ctx.shadowBlur = 0;
            }

            ctx.restore();
        }

        // Draw individual grid
        function drawGrid(size, rotation, angle, depth) {
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const gridExtent = Math.max(canvas.width, canvas.height) * 1.5;

            ctx.save();
            ctx.translate(centerX, centerY);
            ctx.rotate(rotation);

            // Apply perspective transformation based on angle and depth
            const perspective = 1 + depth * 0.001;
            ctx.scale(perspective, perspective * Math.cos(angle));
            ctx.globalAlpha *= Math.max(0.1, 1 - Math.abs(depth) * 0.002);

            // Draw vertical lines
            for (let x = -gridExtent; x <= gridExtent; x += size) {
                ctx.beginPath();
                ctx.moveTo(x, -gridExtent);
                ctx.lineTo(x, gridExtent);
                ctx.stroke();
            }

            // Draw horizontal lines
            for (let y = -gridExtent; y <= gridExtent; y += size) {
                ctx.beginPath();
                ctx.moveTo(-gridExtent, y);
                ctx.lineTo(gridExtent, y);
                ctx.stroke();
            }

            ctx.restore();
        }

        // Add digital glitch effect
        function addGlitchEffect() {
            const glitchCount = Math.floor(Math.random() * 5) + 3;

            for (let i = 0; i < glitchCount; i++) {
                const x = Math.random() * canvas.width;
                const y = Math.random() * canvas.height;
                const width = Math.random() * 100 + 50;
                const height = Math.random() * 20 + 5;

                ctx.fillStyle = `rgba(0, ${Math.random() * 255}, ${Math.random() * 255}, 0.5)`;
                ctx.fillRect(x, y, width, height);
            }
        }
        
        // Update holographic mesh particles
        function updateHolographicMesh(frequencyBands) {
            const time = Date.now() * 0.001;

            // Update color shift based on audio
            const avgFreq = frequencyBands.reduce((a, b) => a + b, 0) / frequencyBands.length;
            holographicMesh.config.colorShift += avgFreq * 2;
            holographicMesh.config.energyFlow = avgFreq;

            // Update particle positions and energy
            for (let i = 0; i < holographicMesh.particles.length; i++) {
                const particle = holographicMesh.particles[i];
                const freqBand = frequencyBands[particle.frequency];

                // Audio-reactive movement
                const displacement = freqBand * 30;
                const wavePhase = time * 2 + particle.phase;

                // Calculate normal direction for displacement
                const length = Math.sqrt(particle.baseX * particle.baseX + particle.baseY * particle.baseY + particle.baseZ * particle.baseZ);
                const normalX = particle.baseX / length;
                const normalY = particle.baseY / length;
                const normalZ = particle.baseZ / length;

                // Apply displacement and wave motion
                particle.x = particle.baseX + normalX * displacement + Math.sin(wavePhase) * 10;
                particle.y = particle.baseY + normalY * displacement + Math.cos(wavePhase * 1.3) * 10;
                particle.z = particle.baseZ + normalZ * displacement + Math.sin(wavePhase * 0.7) * 10;

                // Update energy
                particle.energy = freqBand * 0.7 + Math.sin(wavePhase) * 0.3;
            }

            // Occasionally regenerate mirror faces for dynamic effect
            if (Math.random() < 0.01) {
                selectMirrorFaces();
            }
        }

        // Draw holographic mesh with mirrors
        function drawHolographicMesh(frequencyBands) {
            if (holographicMesh.faces.length === 0) return;

            ctx.save();
            ctx.globalCompositeOperation = 'screen';

            // Sort faces by depth for proper rendering
            const projectedFaces = holographicMesh.faces.map((face, index) => {
                const particles = face.particles.map(i => holographicMesh.particles[i]);
                const rotatedParticles = particles.map(p => rotatePoint(p));
                const projectedParticles = rotatedParticles.map(p => projectPoint(p));

                const avgZ = rotatedParticles.reduce((sum, p) => sum + p.z, 0) / 3;

                return {
                    face,
                    index,
                    particles: projectedParticles,
                    rotatedParticles,
                    avgZ,
                    isMirror: holographicMesh.mirrorFaces.has(index)
                };
            });

            // Sort by depth (back to front)
            projectedFaces.sort((a, b) => a.avgZ - b.avgZ);

            // Draw faces
            for (const projectedFace of projectedFaces) {
                const { particles, isMirror, face } = projectedFace;

                if (particles.length < 3) continue;

                // Calculate face angle for holographic effect
                const center = {
                    x: particles.reduce((sum, p) => sum + p.x, 0) / 3,
                    y: particles.reduce((sum, p) => sum + p.y, 0) / 3
                };

                const angle = Math.atan2(center.y - canvas.height / 2, center.x - canvas.width / 2);
                const audioIntensity = frequencyBands[Math.floor(Math.random() * 5)];

                // Draw face
                ctx.beginPath();
                ctx.moveTo(particles[0].x, particles[0].y);
                for (let i = 1; i < particles.length; i++) {
                    ctx.lineTo(particles[i].x, particles[i].y);
                }
                ctx.closePath();

                if (isMirror) {
                    // Mirror face - highly reflective with special effects
                    const mirrorColor = getHolographicColor(angle, audioIntensity, true);

                    // Create gradient for mirror effect
                    const gradient = ctx.createLinearGradient(
                        particles[0].x, particles[0].y,
                        particles[2].x, particles[2].y
                    );
                    gradient.addColorStop(0, mirrorColor);
                    gradient.addColorStop(0.5, getHolographicColor(angle + Math.PI / 3, audioIntensity, true));
                    gradient.addColorStop(1, mirrorColor);

                    ctx.fillStyle = gradient;
                    ctx.shadowBlur = 15 + audioIntensity * 20;
                    ctx.shadowColor = mirrorColor;
                    ctx.fill();

                    // Add mirror outline
                    ctx.strokeStyle = getHolographicColor(angle + Math.PI, audioIntensity * 1.5, true);
                    ctx.lineWidth = 2 + audioIntensity * 3;
                    ctx.stroke();
                } else {
                    // Regular holographic face
                    const holoColor = getHolographicColor(angle, audioIntensity, false);
                    ctx.fillStyle = holoColor;
                    ctx.shadowBlur = 8 + audioIntensity * 12;
                    ctx.shadowColor = holoColor;
                    ctx.fill();

                    // Subtle outline
                    ctx.strokeStyle = getHolographicColor(angle + Math.PI / 2, audioIntensity * 0.8, false);
                    ctx.lineWidth = 1 + audioIntensity * 2;
                    ctx.stroke();
                }

                // Reset shadow
                ctx.shadowBlur = 0;
            }

            // Draw particle nodes
            for (const particle of holographicMesh.particles) {
                const rotated = rotatePoint(particle);
                const projected = projectPoint(rotated);
                const audioIntensity = frequencyBands[particle.frequency];

                const nodeSize = (3 + audioIntensity * 8) * projected.scale;
                const nodeColor = getHolographicColor(particle.phase, audioIntensity, false);

                ctx.fillStyle = nodeColor;
                ctx.shadowBlur = 10 + audioIntensity * 15;
                ctx.shadowColor = nodeColor;

                ctx.beginPath();
                ctx.arc(projected.x, projected.y, nodeSize, 0, Math.PI * 2);
                ctx.fill();

                ctx.shadowBlur = 0;
            }

            ctx.restore();
        }

        // Draw circular visualizer from art6.1.html
        function drawCircularVisualizer(frequencyBands) {
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const visualizerSize = config.sphereSize * 1.2;
            const bars = 64;
            const barWidth = (Math.PI * 2) / bars;
            
            ctx.save();
            ctx.globalCompositeOperation = 'screen';
            
            // Get audio data if available, otherwise use default values
            let audioData;
            if (analyser) {
                // Use a smaller FFT size for faster response
                const bufferLength = analyser.frequencyBinCount;
                audioData = new Uint8Array(bufferLength);
                analyser.getByteFrequencyData(audioData);
            } else {
                // Create dummy data for when no music is playing
                audioData = new Uint8Array(128);
                for (let i = 0; i < 128; i++) {
                    audioData[i] = 10 + Math.sin(i * 0.1 + time * 2) * 10;
                }
            }
            
            // Draw outer circle only
            for(let i = 0; i < bars; i++) {
                // Apply direct mapping without smoothing for faster response
                const amplitude = audioData[i % audioData.length] / 255;
                const barHeight = visualizerSize * 0.3 * amplitude;
                const angle = i * barWidth;
                
                const x1 = centerX + Math.cos(angle) * visualizerSize;
                const y1 = centerY + Math.sin(angle) * visualizerSize;
                const x2 = centerX + Math.cos(angle) * (visualizerSize + barHeight);
                const y2 = centerY + Math.sin(angle) * (visualizerSize + barHeight);
                
                // Use a single cyan color to match the sphere
                const cyanColor = `rgba(0, 255, 255, 0.7)`;
                
                ctx.beginPath();
                ctx.lineWidth = 2;
                ctx.strokeStyle = cyanColor;
                ctx.moveTo(x1, y1);
                ctx.lineTo(x2, y2);
                ctx.stroke();
            }
            
            ctx.restore();
        }
        
        // Add event listeners
        document.getElementById('uploadButton').addEventListener('click', function() {
            document.getElementById('audioFile').click();
        });
        
        document.getElementById('audioFile').addEventListener('change', handleAudioFile);
        
        document.getElementById('playPauseButton').addEventListener('click', togglePlayPause);
        
        // Toggle recording function
        function toggleRecording() {
            if (!isRecording) {
                startRecording();
            } else {
                stopRecording();
            }
        }

        // Start screen recording
        function startRecording() {
            recordedChunks = [];

            // First ensure audio context is in running state
            if (!audioContext) {
                initAudio();
            }

            if (audioContext && audioContext.state === 'suspended') {
                audioContext.resume();
            }

            // Create a new audio destination for recording
            const audioDestination = audioContext.createMediaStreamDestination();

            // Create a gain node for the recording audio path
            const recordingGain = audioContext.createGain();
            recordingGain.gain.value = 1.5; // Boost recording volume

            // Connect the analyser to the recording path
            // Important: We need to tap into the audio BEFORE the analyser
            if (audioSource) {
                // Connect from the original source if available
                audioSource.connect(recordingGain);
            } else if (analyser) {
                // Otherwise connect from the analyser
                analyser.connect(recordingGain);
            }

            recordingGain.connect(audioDestination);

            // Get the canvas stream
            const canvasStream = canvas.captureStream(60);

            // Get audio tracks from the audio destination
            const audioTracks = audioDestination.stream.getAudioTracks();

            // Create a new MediaStream with both video and audio
            const combinedStream = new MediaStream();

            // Add all tracks from canvas stream
            canvasStream.getTracks().forEach(track => {
                combinedStream.addTrack(track);
            });

            // Add audio tracks
            audioTracks.forEach(track => {
                combinedStream.addTrack(track);
            });

            console.log("Audio tracks added:", audioTracks.length);
            console.log("Total tracks in combined stream:", combinedStream.getTracks().length);

            // Use WebM with VP9 codec with optimized settings
            mediaRecorder = new MediaRecorder(combinedStream, {
                mimeType: 'video/webm; codecs=vp9',
                videoBitsPerSecond: 5000000, // 5 Mbps video
                audioBitsPerSecond: 256000   // 256 kbps audio for better quality
            });

            mediaRecorder.ondataavailable = (e) => {
                if (e.data.size > 0) {
                    recordedChunks.push(e.data);
                }
            };

            mediaRecorder.onstop = () => {
                const blob = new Blob(recordedChunks, { type: 'video/webm' });
                const url = URL.createObjectURL(blob);
                downloadLink.href = url;
                downloadLink.download = 'rotating-sphere.webm';
                downloadLink.textContent = 'Download WebM';
                downloadLink.style.display = 'inline-block';

                isRecording = false;
                recordButton.textContent = 'Record';
                recordButton.style.background = '#333';

                // Add conversion note
                const conversionNote = document.createElement('div');
                conversionNote.textContent = 'Note: For MP4 conversion, use online tools like CloudConvert';
                conversionNote.style.color = '#888';
                conversionNote.style.fontSize = '12px';
                conversionNote.style.marginTop = '5px';
                downloadLink.parentNode.insertBefore(conversionNote, downloadLink.nextSibling);
            };

            mediaRecorder.start(1000); // Collect data every second for more reliable recording
            isRecording = true;
            recordButton.textContent = 'Stop Recording';
            recordButton.style.background = 'rgba(255, 0, 0, 0.7)';
        }

        // Stop screen recording
        function stopRecording() {
            if (mediaRecorder && isRecording) {
                mediaRecorder.stop();
                isRecording = false;
                recordButton.textContent = 'Record';
                recordButton.style.background = '#333';
            }
        }

        // Initialize recording functionality
        function initRecording() {
            recordButton.addEventListener('click', toggleRecording);
        }
        
        // Initialize and start animation
        generateSpherePoints();
        generateTeleportRings();
        generateGridLayers();
        generateHolographicMesh();
        initRecording();
        animate();
    </script>
</body>
</html>
