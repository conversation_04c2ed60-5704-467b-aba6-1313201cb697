<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rotating 3D Sphere</title>
    <style>
        body {
            margin: 0;
            overflow: hidden;
            background: #000;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }
        canvas {
            display: block;
        }
    </style>
</head>
<body>
    <canvas id="sphereCanvas"></canvas>
    
    <div style="position: fixed; bottom: 20px; left: 50%; transform: translateX(-50%); z-index: 10; text-align: center;">
        <button id="uploadButton" style="background: #333; color: #fff; border: 1px solid #555; padding: 8px 15px; border-radius: 4px; cursor: pointer;">Upload Audio</button>
        <button id="playPauseButton" style="background: #333; color: #fff; border: 1px solid #555; padding: 8px 15px; border-radius: 4px; cursor: pointer; margin-left: 5px;" disabled>Play/Pause</button>
        <button id="recordButton" style="background: #333; color: #fff; border: 1px solid #555; padding: 8px 15px; border-radius: 4px; cursor: pointer; margin-left: 5px;">Record</button>
        <input type="file" id="audioFile" accept="audio/*" style="display: none;">
        <div id="audioStatus" style="margin-top: 8px; font-size: 12px; color: #fff;">No audio loaded</div>
        <a id="downloadLink" style="display: none; background: #333; color: #fff; border: 1px solid #555; padding: 8px 15px; border-radius: 4px; cursor: pointer; margin-top: 5px; text-decoration: none;">Download Recording</a>
    </div>

    <script>
        // Get canvas and context
        const canvas = document.getElementById('sphereCanvas');
        const ctx = canvas.getContext('2d');
        
        // Audio variables
        let audioContext, analyser, audioSource, audioData;
        let isPlaying = false;
        const playPauseButton = document.getElementById('playPauseButton');
        const audioStatus = document.getElementById('audioStatus');
        
        // Add recording variables
        let mediaRecorder;
        let recordedChunks = [];
        let isRecording = false;
        const recordButton = document.getElementById('recordButton');
        const downloadLink = document.getElementById('downloadLink');
        
        // Set canvas size
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
        
        // Handle window resize
        window.addEventListener('resize', () => {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        });
        
        // Configuration
        const config = {
            sphereSize: Math.min(window.innerWidth, window.innerHeight) * 0.3,
            rotationSpeed: 0.01,
            backgroundColor: 'rgba(0, 0, 0, 0.1)',
            pointColor: '#222',
            outlineColor: '#0ff',
            ringCount: 5,  // Number of teleporting rings
            ringSpeed: 0.5,  // Speed of ring movement
            ringAcceleration: 0.2  // Base acceleration factor for rings
        };
        
        // Animation variables
        let time = 0;
        let rotation = { x: 0, y: 0, z: 0 };

        // 3D points for sphere shape
        const spherePoints = [];
        // Teleporting rings
        const teleportRings = [];
        
        // Initialize audio context
        function initAudio() {
            audioContext = new (window.AudioContext || window.webkitAudioContext)();
            analyser = audioContext.createAnalyser();
            // Smaller FFT size for more responsive analysis
            analyser.fftSize = 256; // Reduced from default
            audioData = new Uint8Array(analyser.frequencyBinCount);
        }
        
        // Handle audio file upload
        function handleAudioFile(e) {
            const file = e.target.files[0];
            if (!file) return;
            
            const reader = new FileReader();
            audioStatus.textContent = "Loading audio...";
            audioStatus.style.color = "#ffcc00";
            
            reader.onload = function(e) {
                if (!audioContext) initAudio();
                
                if (audioSource) {
                    audioSource.disconnect();
                }
                
                audioContext.decodeAudioData(e.target.result, function(buffer) {
                    audioSource = audioContext.createBufferSource();
                    audioSource.buffer = buffer;
                    audioSource.connect(analyser);
                    analyser.connect(audioContext.destination);
                    
                    audioSource.loop = false; // Change from true to false
                    
                    // Add ended event listener
                    audioSource.onended = function() {
                        isPlaying = false;
                        playPauseButton.textContent = "Play";
                        playPauseButton.disabled = false;
                        audioStatus.textContent = "Playback ended";
                    };
                    
                    audioSource.start(0);
                    isPlaying = true;
                    
                    // Enable and update play/pause button
                    playPauseButton.disabled = false;
                    playPauseButton.textContent = "Pause";
                    
                    audioStatus.textContent = "Playing: " + file.name;
                    audioStatus.style.color = "#00ff66";
                });
            };
            
            reader.readAsArrayBuffer(file);
        }
        
        // Toggle play/pause function
        function togglePlayPause() {
            if (!audioContext || !audioSource) return;
            
            if (isPlaying) {
                // Pause audio
                audioContext.suspend();
                isPlaying = false;
                playPauseButton.textContent = "Play";
            } else {
                // Resume audio
                audioContext.resume();
                isPlaying = true;
                playPauseButton.textContent = "Pause";
            }
        }
        
        // Analyze audio and get frequency bands
        function analyzeAudio() {
            if (!analyser || !isPlaying) {
                return [0, 0, 0, 0, 0]; // Default values when no audio
            }
            
            analyser.getByteFrequencyData(audioData);
            
            // Split frequency data into 5 bands
            const bandSize = Math.floor(audioData.length / 5);
            const bands = [];
            
            for (let i = 0; i < 5; i++) {
                const start = i * bandSize;
                const end = start + bandSize;
                let sum = 0;
                
                for (let j = start; j < end; j++) {
                    sum += audioData[j];
                }
                
                // Normalize to 0-1 and apply faster response
                const value = sum / bandSize / 255;
                bands.push(value); 
            }
            
            return bands;
        }
        
        // Generate sphere points
        function generateSpherePoints() {
            // Clear existing points
            spherePoints.length = 0;
            
            // Generate sphere using latitude and longitude lines
            const latitudeLines = 30;
            const longitudeLines = 30;
            
            // Create latitude lines (horizontal circles)
            for (let lat = 0; lat <= latitudeLines; lat++) {
                const phi = Math.PI * lat / latitudeLines;
                const y = Math.cos(phi);
                const radius = Math.sin(phi);
                
                for (let lon = 0; lon < longitudeLines; lon++) {
                    const theta = 2 * Math.PI * lon / longitudeLines;
                    const x = Math.cos(theta) * radius;
                    const z = Math.sin(theta) * radius;
                    
                    // Scale to size
                    spherePoints.push({ 
                        x: x * config.sphereSize,
                        y: y * config.sphereSize,
                        z: z * config.sphereSize,
                        baseX: x * config.sphereSize,
                        baseY: y * config.sphereSize,
                        baseZ: z * config.sphereSize,
                        freqBand: Math.floor(lat / latitudeLines * 5), // Assign frequency band
                        isLatitude: true
                    });
                }
            }
            
            // Create longitude lines (vertical half-circles)
            for (let lon = 0; lon < longitudeLines; lon++) {
                const theta = 2 * Math.PI * lon / longitudeLines;
                
                for (let lat = 0; lat <= latitudeLines; lat++) {
                    const phi = Math.PI * lat / latitudeLines;
                    const y = Math.cos(phi);
                    const radius = Math.sin(phi);
                    
                    const x = Math.cos(theta) * radius;
                    const z = Math.sin(theta) * radius;
                    
                    // Scale to size
                    spherePoints.push({ 
                        x: x * config.sphereSize,
                        y: y * config.sphereSize,
                        z: z * config.sphereSize,
                        baseX: x * config.sphereSize,
                        baseY: y * config.sphereSize,
                        baseZ: z * config.sphereSize,
                        freqBand: Math.floor(lon / longitudeLines * 5), // Assign frequency band
                        isLongitude: true
                    });
                }
            }
        }
        
        // Generate teleporting rings
        function generateTeleportRings() {
            teleportRings.length = 0;
            
            for (let i = 0; i < config.ringCount; i++) {
                // Assign a frequency band to each ring (0-4)
                const freqBand = Math.floor(Math.random() * 5);
                
                teleportRings.push({
                    radius: config.sphereSize * (1.2 + Math.random() * 0.5),
                    position: Math.random() * Math.PI * 2, // Random position around the sphere
                    speed: (0.5 + Math.random() * 0.5) * config.ringSpeed * (Math.random() > 0.5 ? 1 : -1),
                    baseSpeed: (0.5 + Math.random() * 0.5) * config.ringSpeed * (Math.random() > 0.5 ? 1 : -1),
                    thickness: 1 + Math.random() * 2,
                    freqBand: freqBand, // Store the frequency band for this ring
                    opacity: 0.2 + Math.random() * 0.5,
                    lastBassLevel: 0
                });
            }
        }
        
        // Rotate point around all axes
        function rotatePoint(point) {
            // X-axis rotation
            let y = point.y * Math.cos(rotation.x) - point.z * Math.sin(rotation.x);
            let z = point.y * Math.sin(rotation.x) + point.z * Math.cos(rotation.x);
            
            // Y-axis rotation
            let x = point.x * Math.cos(rotation.y) + z * Math.sin(rotation.y);
            z = -point.x * Math.sin(rotation.y) + z * Math.cos(rotation.y);
            
            // Z-axis rotation
            const tempX = x;
            x = x * Math.cos(rotation.z) - y * Math.sin(rotation.z);
            y = tempX * Math.sin(rotation.z) + y * Math.cos(rotation.z);
            
            return { x, y, z };
        }
        
        // Project 3D point to 2D
        function projectPoint(point) {
            const fov = 300;
            const viewZ = 1.5 * config.sphereSize;
            const scale = fov / (fov + point.z + viewZ);
            
            return {
                x: canvas.width / 2 + point.x * scale,
                y: canvas.height / 2 + point.y * scale,
                scale: scale
            };
        }
        
        // Animation loop
        function animate() {
            // Clear canvas with fade effect
            ctx.fillStyle = config.backgroundColor;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // Update time
            time += 0.01;
            
            // Update rotation
            rotation.y += config.rotationSpeed;
            rotation.x = Math.sin(time * 0.3) * 0.2;
            
            // Analyze audio
            const frequencyBands = analyzeAudio();
            
            // Update points based on audio
            for (const point of spherePoints) {
                const band = frequencyBands[point.freqBand];
                const waveIntensity = band * 0.5; // Scale factor for wave effect
                
                // Calculate displacement direction (normal to sphere surface)
                const normalX = point.baseX / config.sphereSize;
                const normalY = point.baseY / config.sphereSize;
                const normalZ = point.baseZ / config.sphereSize;
                
                // Apply wave effect based on audio frequency
                // Move points along their normal vector instead of scaling
                const displacement = waveIntensity * 20; // Max displacement in pixels
                
                point.x = point.baseX + normalX * displacement;
                point.y = point.baseY + normalY * displacement;
                point.z = point.baseZ + normalZ * displacement;
            }
            
            // Project points to 2D
            const projectedPoints = spherePoints.map(point => {
                const rotated = rotatePoint(point);
                return {
                    original: point,
                    rotated: rotated,
                    projected: projectPoint(rotated)
                };
            });
            
            // Sort points by z-depth
            projectedPoints.sort((a, b) => b.rotated.z - a.rotated.z);
            
            // Draw circular visualizer
            drawCircularVisualizer(frequencyBands);
            
            // Draw teleporting rings
            drawTeleportRings(frequencyBands);
            
            // Draw points
            for (const point of projectedPoints) {
                // Get frequency band for this point
                const band = frequencyBands[point.original.freqBand];
                
                // Adjust size based on audio
                const size = (2 + band * 5) * point.projected.scale;
                
                // Adjust color based on audio intensity
                const hue = 180 + band * 180; // Cyan to pink
                const brightness = 50 + band * 50; // Brighter with higher intensity
                
                // Draw point with glow
                ctx.shadowBlur = 5 + band * 10;
                ctx.shadowColor = `hsl(180, 100%, 50%)`;
                
                ctx.fillStyle = `hsl(180, 80%, 25%)`;
                ctx.beginPath();
                ctx.arc(point.projected.x, point.projected.y, size, 0, Math.PI * 2);
                ctx.fill();
                
                // Reset shadow for performance
                ctx.shadowBlur = 0;
            }
            
            // Add occasional glitch effect
            if (Math.random() > 0.98) {
                addGlitchEffect();
            }
            
            requestAnimationFrame(animate);
        }
        
        // Draw teleporting rings
        function drawTeleportRings(frequencyBands) {
            for (const ring of teleportRings) {
                // Get bass level (using the first frequency band)
                const bassLevel = frequencyBands[0];
                
                // Calculate acceleration based on bass intensity and changes
                const bassDelta = Math.max(0, bassLevel - ring.lastBassLevel);
                const acceleration = bassDelta * config.ringAcceleration;
                
                // Apply acceleration to ring speed
                if (bassLevel > 0.2) {
                    // Accelerate in current direction
                    ring.speed += (ring.speed > 0 ? 1 : -1) * acceleration;
                } else {
                    // Gradually return to base speed
                    ring.speed = ring.speed * 0.95 + ring.baseSpeed * 0.05;
                }
                
                // Store current bass level for next frame
                ring.lastBassLevel = bassLevel;
                
                // Update ring position with current speed
                ring.position += ring.speed * 0.02;
                
                // Reset position if it goes out of bounds
                if (ring.position > Math.PI * 2) ring.position = 0;
                if (ring.position < 0) ring.position = Math.PI * 2;
                
                // Calculate ring orientation based on position
                const phi = ring.position;
                
                // Get frequency band for this ring
                const band = frequencyBands[ring.freqBand];
                const audioIntensity = band;
                const pulseSize = 1 + audioIntensity * 0.3;
                const currentRadius = ring.radius * pulseSize;
                
                // Use fixed color for rings
                const ringColor = `hsl(180, 100%, 70%)`;
                
                // Draw ring
                ctx.beginPath();
                
                for (let theta = 0; theta < Math.PI * 2; theta += 0.05) {
                    const x = Math.sin(phi) * Math.sin(theta) * currentRadius;
                    const y = Math.cos(phi) * currentRadius;
                    const z = Math.sin(phi) * Math.cos(theta) * currentRadius;
                    
                    // Rotate point
                    const rotated = rotatePoint({x, y, z});
                    const projected = projectPoint(rotated);
                    
                    if (theta === 0) {
                        ctx.moveTo(projected.x, projected.y);
                    } else {
                        ctx.lineTo(projected.x, projected.y);
                    }
                }
                
                ctx.closePath();
                
                // Set ring style with glow effect
                ctx.strokeStyle = ringColor.replace(')', `, ${ring.opacity + audioIntensity * 0.3})`).replace('hsl', 'hsla');
                ctx.lineWidth = ring.thickness * (1 + audioIntensity * 0.3);
                
                // Add glow effect
                ctx.shadowBlur = 15 * (1 + audioIntensity);
                ctx.shadowColor = ringColor;
                
                ctx.stroke();
                
                // Reset shadow for performance
                ctx.shadowBlur = 0;
            }
        }
        
        // Add digital glitch effect
        function addGlitchEffect() {
            const glitchCount = Math.floor(Math.random() * 5) + 3;
            
            for (let i = 0; i < glitchCount; i++) {
                const x = Math.random() * canvas.width;
                const y = Math.random() * canvas.height;
                const width = Math.random() * 100 + 50;
                const height = Math.random() * 20 + 5;
                
                ctx.fillStyle = `rgba(0, ${Math.random() * 255}, ${Math.random() * 255}, 0.5)`;
                ctx.fillRect(x, y, width, height);
            }
        }
        
        // Draw circular visualizer from art6.1.html
        function drawCircularVisualizer(frequencyBands) {
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;
            const visualizerSize = config.sphereSize * 1.2;
            const bars = 64;
            const barWidth = (Math.PI * 2) / bars;
            
            ctx.save();
            ctx.globalCompositeOperation = 'screen';
            
            // Get audio data if available, otherwise use default values
            let audioData;
            if (analyser) {
                // Use a smaller FFT size for faster response
                const bufferLength = analyser.frequencyBinCount;
                audioData = new Uint8Array(bufferLength);
                analyser.getByteFrequencyData(audioData);
            } else {
                // Create dummy data for when no music is playing
                audioData = new Uint8Array(128);
                for (let i = 0; i < 128; i++) {
                    audioData[i] = 10 + Math.sin(i * 0.1 + time * 2) * 10;
                }
            }
            
            // Draw outer circle only
            for(let i = 0; i < bars; i++) {
                // Apply direct mapping without smoothing for faster response
                const amplitude = audioData[i % audioData.length] / 255;
                const barHeight = visualizerSize * 0.3 * amplitude;
                const angle = i * barWidth;
                
                const x1 = centerX + Math.cos(angle) * visualizerSize;
                const y1 = centerY + Math.sin(angle) * visualizerSize;
                const x2 = centerX + Math.cos(angle) * (visualizerSize + barHeight);
                const y2 = centerY + Math.sin(angle) * (visualizerSize + barHeight);
                
                // Use a single cyan color to match the sphere
                const cyanColor = `rgba(0, 255, 255, 0.7)`;
                
                ctx.beginPath();
                ctx.lineWidth = 2;
                ctx.strokeStyle = cyanColor;
                ctx.moveTo(x1, y1);
                ctx.lineTo(x2, y2);
                ctx.stroke();
            }
            
            ctx.restore();
        }
        
        // Add event listeners
        document.getElementById('uploadButton').addEventListener('click', function() {
            document.getElementById('audioFile').click();
        });
        
        document.getElementById('audioFile').addEventListener('change', handleAudioFile);
        
        document.getElementById('playPauseButton').addEventListener('click', togglePlayPause);
        
        // Toggle recording function
        function toggleRecording() {
            if (!isRecording) {
                startRecording();
            } else {
                stopRecording();
            }
        }

        // Start screen recording
        function startRecording() {
            recordedChunks = [];

            // First ensure audio context is in running state
            if (!audioContext) {
                initAudio();
            }

            if (audioContext && audioContext.state === 'suspended') {
                audioContext.resume();
            }

            // Create a new audio destination for recording
            const audioDestination = audioContext.createMediaStreamDestination();

            // Create a gain node for the recording audio path
            const recordingGain = audioContext.createGain();
            recordingGain.gain.value = 1.5; // Boost recording volume

            // Connect the analyser to the recording path
            // Important: We need to tap into the audio BEFORE the analyser
            if (audioSource) {
                // Connect from the original source if available
                audioSource.connect(recordingGain);
            } else if (analyser) {
                // Otherwise connect from the analyser
                analyser.connect(recordingGain);
            }

            recordingGain.connect(audioDestination);

            // Get the canvas stream
            const canvasStream = canvas.captureStream(60);

            // Get audio tracks from the audio destination
            const audioTracks = audioDestination.stream.getAudioTracks();

            // Create a new MediaStream with both video and audio
            const combinedStream = new MediaStream();

            // Add all tracks from canvas stream
            canvasStream.getTracks().forEach(track => {
                combinedStream.addTrack(track);
            });

            // Add audio tracks
            audioTracks.forEach(track => {
                combinedStream.addTrack(track);
            });

            console.log("Audio tracks added:", audioTracks.length);
            console.log("Total tracks in combined stream:", combinedStream.getTracks().length);

            // Use WebM with VP9 codec with optimized settings
            mediaRecorder = new MediaRecorder(combinedStream, {
                mimeType: 'video/webm; codecs=vp9',
                videoBitsPerSecond: 5000000, // 5 Mbps video
                audioBitsPerSecond: 256000   // 256 kbps audio for better quality
            });

            mediaRecorder.ondataavailable = (e) => {
                if (e.data.size > 0) {
                    recordedChunks.push(e.data);
                }
            };

            mediaRecorder.onstop = () => {
                const blob = new Blob(recordedChunks, { type: 'video/webm' });
                const url = URL.createObjectURL(blob);
                downloadLink.href = url;
                downloadLink.download = 'rotating-sphere.webm';
                downloadLink.textContent = 'Download WebM';
                downloadLink.style.display = 'inline-block';

                isRecording = false;
                recordButton.textContent = 'Record';
                recordButton.style.background = '#333';

                // Add conversion note
                const conversionNote = document.createElement('div');
                conversionNote.textContent = 'Note: For MP4 conversion, use online tools like CloudConvert';
                conversionNote.style.color = '#888';
                conversionNote.style.fontSize = '12px';
                conversionNote.style.marginTop = '5px';
                downloadLink.parentNode.insertBefore(conversionNote, downloadLink.nextSibling);
            };

            mediaRecorder.start(1000); // Collect data every second for more reliable recording
            isRecording = true;
            recordButton.textContent = 'Stop Recording';
            recordButton.style.background = 'rgba(255, 0, 0, 0.7)';
        }

        // Stop screen recording
        function stopRecording() {
            if (mediaRecorder && isRecording) {
                mediaRecorder.stop();
                isRecording = false;
                recordButton.textContent = 'Record';
                recordButton.style.background = '#333';
            }
        }

        // Initialize recording functionality
        function initRecording() {
            recordButton.addEventListener('click', toggleRecording);
        }
        
        // Initialize and start animation
        generateSpherePoints();
        generateTeleportRings();
        initRecording();
        animate();
    </script>
</body>
</html>
