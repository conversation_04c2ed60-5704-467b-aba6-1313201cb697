<!DOCTYPE html>
<html>
<head>
    <title>Optimized Mandelbulb Viewer</title>
    <style>
        body { margin: 0; overflow: hidden; background: black; }
        canvas { width: 100vw; height: 100vh; display: block; }
        .controls {
            position: fixed;
            top: 10px;
            left: 10px;
            color: white;
            font-family: monospace;
            background: rgba(0,0,0,0.5);
            padding: 10px;
        }
        #quality {
            position: fixed;
            top: 10px;
            right: 10px;
            color: white;
            background: rgba(0,0,0,0.5);
            padding: 10px;
        }
    </style>
</head>
<body>
<div class="controls">
    WASD - Move | Mouse - Look | Q/E - Up/Down<br>
    +/- Keys: Adjust Quality
</div>
<div id="quality">Quality: <span id="qualityValue">50</span>%</div>
<canvas id="canvas"></canvas>
<script>
const canvas = document.getElementById('canvas');
const gl = canvas.getContext('webgl');

// Camera state
let cameraPos = [0.0, 0.0, -4.0];
let cameraRot = [0.0, 0.0];
let quality = 0.5; // 50% quality by default

// Resize handler with quality scaling
function resize() {
    const width = window.innerWidth * quality;
    const height = window.innerHeight * quality;
    canvas.style.width = window.innerWidth + 'px';
    canvas.style.height = window.innerHeight + 'px';
    canvas.width = width;
    canvas.height = height;
    gl.viewport(0, 0, width, height);
}
window.addEventListener('resize', resize);
resize();

const vertexShaderSource = `
    attribute vec2 position;
    void main() {
        gl_Position = vec4(position, 0.0, 1.0);
    }
`;

const fragmentShaderSource = `
    precision mediump float;
    uniform vec2 resolution;
    uniform vec3 cameraPos;
    uniform vec2 cameraRot;

    float mandelbulb(vec3 pos) {
        vec3 z = pos;
        float dr = 1.0;
        float r = 0.0;
        
        // Reduced iterations for better performance
        for (int i = 0; i < 8; i++) {
            r = length(z);
            if (r > 2.0) break;
            
            float theta = acos(z.z / r);
            float phi = atan(z.y, z.x);
            dr = pow(r, 7.0) * 8.0 * dr + 1.0;
            
            float zr = pow(r, 8.0);
            theta = theta * 8.0;
            phi = phi * 8.0;
            
            z = zr * vec3(
                sin(theta) * cos(phi),
                sin(theta) * sin(phi),
                cos(theta)
            );
            z += pos;
        }
        return 0.5 * log(r) * r / dr;
    }

    vec3 getRayDir(vec2 uv, vec3 camPos, vec2 camRot) {
        vec3 forward = vec3(
            cos(camRot.y) * sin(camRot.x),
            sin(camRot.y),
            cos(camRot.y) * cos(camRot.x)
        );
        vec3 right = normalize(cross(forward, vec3(0.0, 1.0, 0.0)));
        vec3 up = normalize(cross(right, forward));
        return normalize(forward + uv.x * right + uv.y * up);
    }

    // Simplified normal estimation
    vec3 estimateNormal(vec3 p) {
        float d = 0.01; // Increased epsilon for better performance
        return normalize(vec3(
            mandelbulb(p + vec3(d,0,0)) - mandelbulb(p - vec3(d,0,0)),
            mandelbulb(p + vec3(0,d,0)) - mandelbulb(p - vec3(0,d,0)),
            mandelbulb(p + vec3(0,0,d)) - mandelbulb(p - vec3(0,0,d))
        ));
    }

    void main() {
        vec2 uv = (gl_FragCoord.xy * 2.0 - resolution) / min(resolution.x, resolution.y);
        vec3 rayDir = getRayDir(uv, cameraPos, cameraRot);
        
        float t = 0.0;
        vec3 col = vec3(0.0);
        
        // Reduced ray steps for better performance
        for(int i = 0; i < 50; i++) {
            vec3 pos = cameraPos + rayDir * t;
            float d = mandelbulb(pos);
            
            if(abs(d) < 0.01) { // Increased threshold for better performance
                vec3 normal = estimateNormal(pos);
                vec3 light = normalize(vec3(1.0, 1.0, -1.0));
                float diff = max(0.0, dot(normal, light));
                col = vec3(1.0) * diff;
                break;
            }
            
            t += d;
            if(t > 10.0) break; // Reduced maximum distance
        }
        
        gl_FragColor = vec4(col, 1.0);
    }
`;

// Create and compile shaders
function createShader(type, source) {
    const shader = gl.createShader(type);
    gl.shaderSource(shader, source);
    gl.compileShader(shader);
    if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
        console.error(gl.getShaderInfoLog(shader));
    }
    return shader;
}

const program = gl.createProgram();
gl.attachShader(program, createShader(gl.VERTEX_SHADER, vertexShaderSource));
gl.attachShader(program, createShader(gl.FRAGMENT_SHADER, fragmentShaderSource));
gl.linkProgram(program);
gl.useProgram(program);

// Create vertex buffer
const vertices = new Float32Array([-1,-1, 1,-1, -1,1, 1,1]);
const buffer = gl.createBuffer();
gl.bindBuffer(gl.ARRAY_BUFFER, buffer);
gl.bufferData(gl.ARRAY_BUFFER, vertices, gl.STATIC_DRAW);

// Set up attributes and uniforms
const position = gl.getAttribLocation(program, 'position');
gl.enableVertexAttribArray(position);
gl.vertexAttribPointer(position, 2, gl.FLOAT, false, 0, 0);

const uniforms = {
    resolution: gl.getUniformLocation(program, 'resolution'),
    cameraPos: gl.getUniformLocation(program, 'cameraPos'),
    cameraRot: gl.getUniformLocation(program, 'cameraRot')
};

// Input handling
const keys = new Set();
window.addEventListener('keydown', e => {
    const key = e.key.toLowerCase();
    keys.add(key);
    
    // Quality adjustment
    if (key === '+' || key === '=') {
        quality = Math.min(1.0, quality + 0.1);
        document.getElementById('qualityValue').textContent = Math.round(quality * 100);
        resize();
    }
    if (key === '-') {
        quality = Math.max(0.1, quality - 0.1);
        document.getElementById('qualityValue').textContent = Math.round(quality * 100);
        resize();
    }
});
window.addEventListener('keyup', e => keys.delete(e.key.toLowerCase()));

// Throttled mouse movement
let lastMouseMove = 0;
canvas.addEventListener('mousemove', e => {
    const now = performance.now();
    if (now - lastMouseMove > 16 && (e.buttons & 1)) { // Limit to ~60fps
        cameraRot[0] += e.movementX * 0.01;
        cameraRot[1] = Math.max(-Math.PI/2, Math.min(Math.PI/2, cameraRot[1] - e.movementY * 0.01));
        lastMouseMove = now;
    }
});

// Throttled render loop
let lastRender = 0;
function render(now) {
    if (now - lastRender >= 16) { // Limit to ~60fps
        const speed = 0.1;
        if (keys.has('w')) {
            cameraPos[0] += Math.sin(cameraRot[0]) * speed;
            cameraPos[2] += Math.cos(cameraRot[0]) * speed;
        }
        if (keys.has('s')) {
            cameraPos[0] -= Math.sin(cameraRot[0]) * speed;
            cameraPos[2] -= Math.cos(cameraRot[0]) * speed;
        }
        if (keys.has('a')) {
            cameraPos[0] -= Math.cos(cameraRot[0]) * speed;
            cameraPos[2] += Math.sin(cameraRot[0]) * speed;
        }
        if (keys.has('d')) {
            cameraPos[0] += Math.cos(cameraRot[0]) * speed;
            cameraPos[2] -= Math.sin(cameraRot[0]) * speed;
        }
        if (keys.has('q')) cameraPos[1] += speed;
        if (keys.has('e')) cameraPos[1] -= speed;

        gl.uniform2f(uniforms.resolution, canvas.width, canvas.height);
        gl.uniform3f(uniforms.cameraPos, ...cameraPos);
        gl.uniform2f(uniforms.cameraRot, ...cameraRot);
        
        gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);
        lastRender = now;
    }
    requestAnimationFrame(render);
}

requestAnimationFrame(render);
</script>
</body>
</html>
