<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Space-Time Visualization</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&display=swap');

        body {
            margin: 0;
            padding: 0;
            background: radial-gradient(ellipse at center, #001122 0%, #000000 70%);
            overflow: hidden;
            font-family: 'Orbitron', monospace;
            color: #fff;
        }

        #canvas {
            display: block;
            cursor: crosshair;
            filter: drop-shadow(0 0 20px rgba(0, 255, 255, 0.3));
        }

        #controls {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 100;
            background: linear-gradient(135deg, rgba(0, 20, 40, 0.9), rgba(0, 10, 30, 0.8));
            padding: 20px;
            border-radius: 15px;
            border: 2px solid rgba(0, 255, 255, 0.3);
            box-shadow: 0 0 30px rgba(0, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }

        .control-group {
            margin-bottom: 15px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: #00ffff;
            font-weight: 700;
            text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
            font-size: 14px;
        }

        input[type="range"] {
            width: 180px;
            height: 6px;
            background: linear-gradient(90deg, #001122, #0066cc);
            border-radius: 3px;
            outline: none;
            -webkit-appearance: none;
        }

        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            width: 18px;
            height: 18px;
            background: radial-gradient(circle, #00ffff, #0088cc);
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 0 15px rgba(0, 255, 255, 0.8);
        }

        input[type="range"]::-moz-range-thumb {
            width: 18px;
            height: 18px;
            background: radial-gradient(circle, #00ffff, #0088cc);
            border-radius: 50%;
            cursor: pointer;
            border: none;
            box-shadow: 0 0 15px rgba(0, 255, 255, 0.8);
        }

        button {
            background: linear-gradient(135deg, #00ffff, #0088cc);
            color: #000;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            cursor: pointer;
            margin-right: 8px;
            font-family: 'Orbitron', monospace;
            font-weight: 700;
            text-transform: uppercase;
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.4);
            transition: all 0.3s ease;
        }

        button:hover {
            background: linear-gradient(135deg, #ffffff, #00ffff);
            box-shadow: 0 0 30px rgba(255, 255, 255, 0.6);
            transform: translateY(-2px);
        }

        #info {
            position: absolute;
            bottom: 20px;
            left: 20px;
            color: #00ccff;
            font-size: 14px;
            font-weight: 400;
            text-shadow: 0 0 10px rgba(0, 204, 255, 0.5);
        }

        #title {
            position: absolute;
            top: 20px;
            right: 20px;
            font-size: 24px;
            font-weight: 900;
            color: #00ffff;
            text-shadow: 0 0 20px rgba(0, 255, 255, 0.8);
            text-transform: uppercase;
            letter-spacing: 2px;
        }
    </style>
</head>
<body>
    <canvas id="canvas"></canvas>
    
    <div id="title">Spacetime Curvature</div>

    <div id="controls">
        <div class="control-group">
            <label>Mass Intensity: <span id="massValue">50</span></label>
            <input type="range" id="massSlider" min="10" max="200" value="50">
        </div>
        <div class="control-group">
            <label>Grid Resolution: <span id="densityValue">20</span></label>
            <input type="range" id="densitySlider" min="10" max="40" value="20">
        </div>
        <div class="control-group">
            <label>Temporal Flow: <span id="timeValue">1</span></label>
            <input type="range" id="timeSlider" min="0.1" max="3" step="0.1" value="1">
        </div>
        <div class="control-group">
            <button id="pauseBtn">Pause</button>
            <button id="resetBtn">Reset</button>
            <button id="presetBtn">Preset</button>
        </div>
    </div>

    <div id="info">
        Click to add gravitational masses • Einstein's General Relativity Visualization
    </div>

    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // Set canvas size
        function resizeCanvas() {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        }
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);
        
        // Simulation variables
        let masses = [];
        let gridDensity = 20;
        let timeSpeed = 1;
        let isPaused = false;
        let time = 0;
        
        // Controls
        const massSlider = document.getElementById('massSlider');
        const densitySlider = document.getElementById('densitySlider');
        const timeSlider = document.getElementById('timeSlider');
        const pauseBtn = document.getElementById('pauseBtn');
        const resetBtn = document.getElementById('resetBtn');
        const presetBtn = document.getElementById('presetBtn');

        massSlider.addEventListener('input', (e) => {
            document.getElementById('massValue').textContent = e.target.value;
        });

        densitySlider.addEventListener('input', (e) => {
            gridDensity = parseInt(e.target.value);
            document.getElementById('densityValue').textContent = e.target.value;
        });

        timeSlider.addEventListener('input', (e) => {
            timeSpeed = parseFloat(e.target.value);
            document.getElementById('timeValue').textContent = e.target.value;
        });

        pauseBtn.addEventListener('click', () => {
            isPaused = !isPaused;
            pauseBtn.textContent = isPaused ? 'Resume' : 'Pause';
        });

        resetBtn.addEventListener('click', () => {
            masses = [];
            time = 0;
        });

        presetBtn.addEventListener('click', () => {
            masses = [
                { x: canvas.width * 0.3, y: canvas.height * 0.4, mass: 150, radius: Math.sqrt(150) * 2 },
                { x: canvas.width * 0.7, y: canvas.height * 0.6, mass: 100, radius: Math.sqrt(100) * 2 },
                { x: canvas.width * 0.5, y: canvas.height * 0.2, mass: 80, radius: Math.sqrt(80) * 2 }
            ];
        });
        
        // Add mass on click
        canvas.addEventListener('click', (e) => {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            const mass = parseInt(massSlider.value);
            
            masses.push({
                x: x,
                y: y,
                mass: mass,
                radius: Math.sqrt(mass) * 2
            });
        });
        
        // Calculate gravitational field at point
        function getFieldStrength(x, y) {
            let totalField = 0;
            masses.forEach(mass => {
                const dx = x - mass.x;
                const dy = y - mass.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                if (distance > 0) {
                    totalField += mass.mass / (distance * distance);
                }
            });
            return Math.min(totalField, 100); // Cap the field strength
        }
        
        // Draw spacetime grid with advanced visual effects
        function drawSpacetimeGrid() {
            const gridSize = canvas.width / gridDensity;

            // Create gradient for grid lines
            const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
            gradient.addColorStop(0, 'rgba(0, 255, 255, 0.8)');
            gradient.addColorStop(0.5, 'rgba(0, 150, 255, 0.6)');
            gradient.addColorStop(1, 'rgba(100, 200, 255, 0.4)');

            ctx.strokeStyle = gradient;
            ctx.lineWidth = 1.5;
            ctx.shadowColor = 'rgba(0, 255, 255, 0.5)';
            ctx.shadowBlur = 3;

            // Draw grid lines with sophisticated curvature
            for (let i = 0; i <= gridDensity; i++) {
                // Vertical lines with enhanced warping
                ctx.beginPath();
                for (let j = 0; j <= gridDensity; j++) {
                    const x = i * gridSize;
                    const y = j * gridSize;
                    const field = getFieldStrength(x, y);
                    const warp = field * 8; // Enhanced warp factor

                    // More sophisticated warping with multiple wave components
                    const warpedX = x +
                        Math.sin(time * 0.008 + y * 0.005) * warp * 0.3 +
                        Math.cos(time * 0.012 + y * 0.008) * warp * 0.2;
                    const warpedY = y +
                        Math.cos(time * 0.008 + x * 0.005) * warp * 0.3 +
                        Math.sin(time * 0.015 + x * 0.007) * warp * 0.2;

                    if (j === 0) {
                        ctx.moveTo(warpedX, warpedY);
                    } else {
                        ctx.lineTo(warpedX, warpedY);
                    }
                }

                // Vary opacity based on field strength
                const avgField = getFieldStrength(i * gridSize, canvas.height / 2);
                ctx.globalAlpha = 0.2 + Math.min(avgField * 0.01, 0.6);
                ctx.stroke();

                // Horizontal lines with enhanced warping
                ctx.beginPath();
                for (let j = 0; j <= gridDensity; j++) {
                    const x = j * gridSize;
                    const y = i * gridSize;
                    const field = getFieldStrength(x, y);
                    const warp = field * 8;

                    const warpedX = x +
                        Math.sin(time * 0.008 + y * 0.005) * warp * 0.3 +
                        Math.cos(time * 0.012 + y * 0.008) * warp * 0.2;
                    const warpedY = y +
                        Math.cos(time * 0.008 + x * 0.005) * warp * 0.3 +
                        Math.sin(time * 0.015 + x * 0.007) * warp * 0.2;

                    if (j === 0) {
                        ctx.moveTo(warpedX, warpedY);
                    } else {
                        ctx.lineTo(warpedX, warpedY);
                    }
                }
                ctx.stroke();
            }

            ctx.globalAlpha = 1;
            ctx.shadowBlur = 0;
        }
        
        // Draw masses with sophisticated visual effects
        function drawMasses() {
            masses.forEach((mass, index) => {
                // Multi-layered gravitational field visualization
                const maxRadius = mass.radius * 6;

                // Outer energy field
                const outerGradient = ctx.createRadialGradient(
                    mass.x, mass.y, 0,
                    mass.x, mass.y, maxRadius
                );
                outerGradient.addColorStop(0, 'rgba(255, 255, 255, 0.9)');
                outerGradient.addColorStop(0.2, 'rgba(0, 255, 255, 0.7)');
                outerGradient.addColorStop(0.4, 'rgba(0, 150, 255, 0.5)');
                outerGradient.addColorStop(0.7, 'rgba(100, 0, 255, 0.3)');
                outerGradient.addColorStop(1, 'rgba(255, 0, 100, 0.1)');

                ctx.fillStyle = outerGradient;
                ctx.beginPath();
                ctx.arc(mass.x, mass.y, maxRadius, 0, Math.PI * 2);
                ctx.fill();

                // Inner energy core
                const innerGradient = ctx.createRadialGradient(
                    mass.x, mass.y, 0,
                    mass.x, mass.y, mass.radius * 2
                );
                innerGradient.addColorStop(0, 'rgba(255, 255, 255, 1)');
                innerGradient.addColorStop(0.3, 'rgba(255, 255, 0, 0.8)');
                innerGradient.addColorStop(0.6, 'rgba(255, 150, 0, 0.6)');
                innerGradient.addColorStop(1, 'rgba(255, 0, 0, 0.3)');

                ctx.fillStyle = innerGradient;
                ctx.beginPath();
                ctx.arc(mass.x, mass.y, mass.radius * 2, 0, Math.PI * 2);
                ctx.fill();

                // Pulsing core effect
                const pulseRadius = mass.radius * (1 + Math.sin(time * 0.05 + index) * 0.2);
                const coreGradient = ctx.createRadialGradient(
                    mass.x, mass.y, 0,
                    mass.x, mass.y, pulseRadius
                );
                coreGradient.addColorStop(0, 'rgba(255, 255, 255, 1)');
                coreGradient.addColorStop(0.5, 'rgba(200, 255, 255, 0.8)');
                coreGradient.addColorStop(1, 'rgba(0, 255, 255, 0.4)');

                ctx.fillStyle = coreGradient;
                ctx.shadowColor = 'rgba(255, 255, 255, 0.8)';
                ctx.shadowBlur = 20;
                ctx.beginPath();
                ctx.arc(mass.x, mass.y, pulseRadius, 0, Math.PI * 2);
                ctx.fill();
                ctx.shadowBlur = 0;

                // Draw mass value with glow effect
                ctx.fillStyle = '#000';
                ctx.font = 'bold 14px Orbitron';
                ctx.textAlign = 'center';
                ctx.shadowColor = 'rgba(255, 255, 255, 0.8)';
                ctx.shadowBlur = 5;
                ctx.fillText(mass.mass.toString(), mass.x, mass.y + 5);
                ctx.shadowBlur = 0;
            });
        }
        
        // Draw advanced time dilation and gravitational wave effects
        function drawTimeDilation() {
            masses.forEach((mass, index) => {
                const maxRadius = mass.radius * 8;

                // Gravitational wave rings
                for (let r = mass.radius * 2; r < maxRadius; r += 15) {
                    const alpha = 1 - (r - mass.radius * 2) / (maxRadius - mass.radius * 2);
                    const waveOffset = Math.sin(time * 0.03 - r * 0.02) * 3;

                    // Create shimmering effect
                    const hue = (time * 0.5 + r * 0.1 + index * 60) % 360;
                    ctx.strokeStyle = `hsla(${hue}, 100%, 70%, ${alpha * 0.4})`;
                    ctx.lineWidth = 2 + Math.sin(time * 0.02 + r * 0.01) * 0.5;
                    ctx.shadowColor = ctx.strokeStyle;
                    ctx.shadowBlur = 8;

                    ctx.beginPath();
                    ctx.arc(mass.x, mass.y, r + waveOffset, 0, Math.PI * 2);
                    ctx.stroke();
                }

                // Event horizon visualization
                ctx.strokeStyle = 'rgba(255, 255, 255, 0.6)';
                ctx.lineWidth = 3;
                ctx.setLineDash([5, 5]);
                ctx.shadowColor = 'rgba(255, 255, 255, 0.8)';
                ctx.shadowBlur = 10;
                ctx.beginPath();
                ctx.arc(mass.x, mass.y, mass.radius * 1.5, 0, Math.PI * 2);
                ctx.stroke();
                ctx.setLineDash([]);
                ctx.shadowBlur = 0;
            });
        }
        
        // Draw enhanced cosmic background
        function drawCosmicBackground() {
            // Nebula-like background
            const nebulaGradient = ctx.createRadialGradient(
                canvas.width * 0.3, canvas.height * 0.3, 0,
                canvas.width * 0.7, canvas.height * 0.7, canvas.width
            );
            nebulaGradient.addColorStop(0, 'rgba(50, 0, 100, 0.3)');
            nebulaGradient.addColorStop(0.5, 'rgba(0, 50, 150, 0.2)');
            nebulaGradient.addColorStop(1, 'rgba(0, 0, 50, 0.1)');

            ctx.fillStyle = nebulaGradient;
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // Enhanced star field
            ctx.fillStyle = '#fff';
            for (let i = 0; i < 200; i++) {
                const x = (i * 137.5) % canvas.width;
                const y = (i * 211.3) % canvas.height;
                const twinkle = (Math.sin(time * 0.008 + i * 0.1) + 1) * 0.5;
                const size = 0.5 + twinkle * 1.5;

                ctx.globalAlpha = twinkle * 0.8;
                ctx.shadowColor = '#fff';
                ctx.shadowBlur = size * 2;
                ctx.fillRect(x, y, size, size);
            }

            // Distant galaxies
            for (let i = 0; i < 20; i++) {
                const x = (i * 317.7) % canvas.width;
                const y = (i * 419.3) % canvas.height;
                const spiral = time * 0.001 + i;

                ctx.globalAlpha = 0.3;
                ctx.strokeStyle = `hsl(${200 + i * 10}, 70%, 60%)`;
                ctx.lineWidth = 1;
                ctx.shadowBlur = 5;

                ctx.beginPath();
                for (let angle = 0; angle < Math.PI * 4; angle += 0.1) {
                    const radius = angle * 2;
                    const spiralX = x + Math.cos(angle + spiral) * radius;
                    const spiralY = y + Math.sin(angle + spiral) * radius;

                    if (angle === 0) {
                        ctx.moveTo(spiralX, spiralY);
                    } else {
                        ctx.lineTo(spiralX, spiralY);
                    }
                }
                ctx.stroke();
            }

            ctx.globalAlpha = 1;
            ctx.shadowBlur = 0;
        }

        // Main animation loop
        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            if (!isPaused) {
                time += timeSpeed;
            }

            drawCosmicBackground();
            drawSpacetimeGrid();
            drawTimeDilation();
            drawMasses();

            requestAnimationFrame(animate);
        }
        
        // Start animation
        animate();
    </script>
</body>
</html>
