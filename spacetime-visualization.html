<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Space-Time Visualization</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            overflow: hidden;
            font-family: 'Courier New', monospace;
            color: #fff;
        }
        
        #canvas {
            display: block;
            cursor: crosshair;
        }
        
        #controls {
            position: absolute;
            top: 20px;
            left: 20px;
            z-index: 100;
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #333;
        }
        
        .control-group {
            margin-bottom: 10px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            color: #0ff;
        }
        
        input[type="range"] {
            width: 150px;
        }
        
        button {
            background: #0ff;
            color: #000;
            border: none;
            padding: 5px 10px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 5px;
        }
        
        button:hover {
            background: #fff;
        }
        
        #info {
            position: absolute;
            bottom: 20px;
            left: 20px;
            color: #888;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <canvas id="canvas"></canvas>
    
    <div id="controls">
        <div class="control-group">
            <label>Mass: <span id="massValue">50</span></label>
            <input type="range" id="massSlider" min="10" max="200" value="50">
        </div>
        <div class="control-group">
            <label>Grid Density: <span id="densityValue">20</span></label>
            <input type="range" id="densitySlider" min="10" max="40" value="20">
        </div>
        <div class="control-group">
            <label>Time Speed: <span id="timeValue">1</span></label>
            <input type="range" id="timeSlider" min="0.1" max="3" step="0.1" value="1">
        </div>
        <div class="control-group">
            <button id="pauseBtn">Pause</button>
            <button id="resetBtn">Reset</button>
        </div>
    </div>
    
    <div id="info">
        Click to add gravitational masses • Spacetime curvature visualization
    </div>

    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // Set canvas size
        function resizeCanvas() {
            canvas.width = window.innerWidth;
            canvas.height = window.innerHeight;
        }
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);
        
        // Simulation variables
        let masses = [];
        let gridDensity = 20;
        let timeSpeed = 1;
        let isPaused = false;
        let time = 0;
        
        // Controls
        const massSlider = document.getElementById('massSlider');
        const densitySlider = document.getElementById('densitySlider');
        const timeSlider = document.getElementById('timeSlider');
        const pauseBtn = document.getElementById('pauseBtn');
        const resetBtn = document.getElementById('resetBtn');
        
        massSlider.addEventListener('input', (e) => {
            document.getElementById('massValue').textContent = e.target.value;
        });
        
        densitySlider.addEventListener('input', (e) => {
            gridDensity = parseInt(e.target.value);
            document.getElementById('densityValue').textContent = e.target.value;
        });
        
        timeSlider.addEventListener('input', (e) => {
            timeSpeed = parseFloat(e.target.value);
            document.getElementById('timeValue').textContent = e.target.value;
        });
        
        pauseBtn.addEventListener('click', () => {
            isPaused = !isPaused;
            pauseBtn.textContent = isPaused ? 'Resume' : 'Pause';
        });
        
        resetBtn.addEventListener('click', () => {
            masses = [];
            time = 0;
        });
        
        // Add mass on click
        canvas.addEventListener('click', (e) => {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            const mass = parseInt(massSlider.value);
            
            masses.push({
                x: x,
                y: y,
                mass: mass,
                radius: Math.sqrt(mass) * 2
            });
        });
        
        // Calculate gravitational field at point
        function getFieldStrength(x, y) {
            let totalField = 0;
            masses.forEach(mass => {
                const dx = x - mass.x;
                const dy = y - mass.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                if (distance > 0) {
                    totalField += mass.mass / (distance * distance);
                }
            });
            return Math.min(totalField, 100); // Cap the field strength
        }
        
        // Draw spacetime grid
        function drawSpacetimeGrid() {
            const gridSize = canvas.width / gridDensity;
            
            ctx.strokeStyle = '#0ff';
            ctx.lineWidth = 1;
            
            // Draw grid lines with curvature
            for (let i = 0; i <= gridDensity; i++) {
                // Vertical lines
                ctx.beginPath();
                for (let j = 0; j <= gridDensity; j++) {
                    const x = i * gridSize;
                    const y = j * gridSize;
                    const field = getFieldStrength(x, y);
                    const warp = field * 5; // Warp factor
                    
                    const warpedX = x + Math.sin(time * 0.01 + y * 0.01) * warp * 0.5;
                    const warpedY = y + Math.cos(time * 0.01 + x * 0.01) * warp * 0.5;
                    
                    if (j === 0) {
                        ctx.moveTo(warpedX, warpedY);
                    } else {
                        ctx.lineTo(warpedX, warpedY);
                    }
                }
                ctx.globalAlpha = 0.3;
                ctx.stroke();
                
                // Horizontal lines
                ctx.beginPath();
                for (let j = 0; j <= gridDensity; j++) {
                    const x = j * gridSize;
                    const y = i * gridSize;
                    const field = getFieldStrength(x, y);
                    const warp = field * 5;
                    
                    const warpedX = x + Math.sin(time * 0.01 + y * 0.01) * warp * 0.5;
                    const warpedY = y + Math.cos(time * 0.01 + x * 0.01) * warp * 0.5;
                    
                    if (j === 0) {
                        ctx.moveTo(warpedX, warpedY);
                    } else {
                        ctx.lineTo(warpedX, warpedY);
                    }
                }
                ctx.stroke();
            }
            ctx.globalAlpha = 1;
        }
        
        // Draw masses
        function drawMasses() {
            masses.forEach(mass => {
                // Draw gravitational field visualization
                const gradient = ctx.createRadialGradient(
                    mass.x, mass.y, 0,
                    mass.x, mass.y, mass.radius * 4
                );
                gradient.addColorStop(0, 'rgba(255, 255, 0, 0.8)');
                gradient.addColorStop(0.5, 'rgba(255, 100, 0, 0.4)');
                gradient.addColorStop(1, 'rgba(255, 0, 0, 0.1)');
                
                ctx.fillStyle = gradient;
                ctx.beginPath();
                ctx.arc(mass.x, mass.y, mass.radius * 4, 0, Math.PI * 2);
                ctx.fill();
                
                // Draw the mass itself
                ctx.fillStyle = '#fff';
                ctx.beginPath();
                ctx.arc(mass.x, mass.y, mass.radius, 0, Math.PI * 2);
                ctx.fill();
                
                // Draw mass value
                ctx.fillStyle = '#000';
                ctx.font = '12px Courier New';
                ctx.textAlign = 'center';
                ctx.fillText(mass.mass.toString(), mass.x, mass.y + 4);
            });
        }
        
        // Draw time dilation effect
        function drawTimeDilation() {
            masses.forEach(mass => {
                const maxRadius = mass.radius * 6;
                for (let r = mass.radius; r < maxRadius; r += 10) {
                    const alpha = 1 - (r - mass.radius) / (maxRadius - mass.radius);
                    ctx.strokeStyle = `rgba(0, 255, 255, ${alpha * 0.3})`;
                    ctx.lineWidth = 2;
                    ctx.beginPath();
                    ctx.arc(mass.x, mass.y, r, 0, Math.PI * 2);
                    ctx.stroke();
                }
            });
        }
        
        // Main animation loop
        function animate() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            if (!isPaused) {
                time += timeSpeed;
            }
            
            // Draw background stars
            ctx.fillStyle = '#fff';
            for (let i = 0; i < 100; i++) {
                const x = (i * 137) % canvas.width;
                const y = (i * 211) % canvas.height;
                const size = (Math.sin(time * 0.01 + i) + 1) * 0.5;
                ctx.globalAlpha = size;
                ctx.fillRect(x, y, 1, 1);
            }
            ctx.globalAlpha = 1;
            
            drawSpacetimeGrid();
            drawTimeDilation();
            drawMasses();
            
            requestAnimationFrame(animate);
        }
        
        // Start animation
        animate();
    </script>
</body>
</html>
